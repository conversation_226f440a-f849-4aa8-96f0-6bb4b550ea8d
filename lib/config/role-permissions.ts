import { AttendanceType } from '@/lib/domain/entities/absence'

// Use existing role types from the codebase (SECURE)
export type UserRole = 'student' | 'admin' | 'super_admin'

export interface RoleConfig {
  allowedPages: string[] // Page patterns this role can access
  redirectTo: string // De<PERSON>ult redirect for this role
  attendanceTypes: AttendanceType[] | ['qr-display'] | ['all'] // Which attendance types they can handle
  navigation: {
    label: string
    path: string
    icon?: string
  }[]
}

export const ROLE_CONFIG: Record<UserRole, RoleConfig> = {
  student: {
    allowedPages: ['/student/home', '/student/profile'],
    redirectTo: '/student/home',
    attendanceTypes: ['qr-display'], // Can only display QR code
    navigation: [
      { label: 'Home', path: '/student/home', icon: 'home' },
      { label: 'Profil', path: '/student/profile', icon: 'user' }, // Indonesian label
    ],
  },

  admin: {
    // FIXED: Admin uses /admin/home (scanner) not /admin/scanner
    allowedPages: ['/admin/home', '/admin/reports', '/admin/profile'],
    redirectTo: '/admin/home', // FIXED: Admin redirects to scanner page (home)
    attendanceTypes: [
      AttendanceType.ZUHR,
      AttendanceType.ASR,
      AttendanceType.DISMISSAL,
      AttendanceType.IJIN,
    ], // Use actual enum values
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' }, // FIXED: Scanner is at /admin/home
      { label: 'Laporan', path: '/admin/reports', icon: 'file-text' }, // Indonesian label
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },

  super_admin: {
    allowedPages: ['/admin/*'], // Full system access
    redirectTo: '/admin/home',
    attendanceTypes: ['all'], // All attendance types
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' }, // FIXED: Scanner is at /admin/home
      { label: 'Laporan', path: '/admin/reports', icon: 'file-text' }, // Indonesian label
      { label: 'Kelas', path: '/admin/classes', icon: 'graduation-cap' }, // Indonesian label
      { label: 'Siswa', path: '/admin/users', icon: 'users' }, // Indonesian label
      { label: 'Admin', path: '/admin/admins', icon: 'shield' }, // Indonesian label
      { label: 'Sesi', path: '/admin/sessions', icon: 'clock' }, // Indonesian label
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },
}

// SECURE helper functions with proper validation
export function canAccessPage(role: UserRole, page: string): boolean {
  if (!role || !page) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  return config.allowedPages.some(pattern => {
    if (pattern.startsWith('!')) {
      // Exclusion pattern (e.g., '!/admin/users/*')
      const excludePattern = pattern.slice(1)
      return !matchesPattern(page, excludePattern)
    }
    return matchesPattern(page, pattern)
  })
}

export function getNavigationItems(role: UserRole) {
  const config = ROLE_CONFIG[role]
  return config ? config.navigation : []
}

export function canHandleAttendanceType(role: UserRole, type: AttendanceType | string): boolean {
  if (!role || !type) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  // Special case for students - they can only display QR
  if (role === 'student') {
    return type === 'qr-display'
  }

  // Special case for super_admin - they can handle all types
  if (role === 'super_admin') {
    return true
  }

  // For admin role, check against specific attendance types
  return config.attendanceTypes.includes(type as any)
}

export function getDefaultRedirect(role: UserRole): string {
  const config = ROLE_CONFIG[role]
  return config ? config.redirectTo : '/'
}

// SECURE pattern matching with input validation
function matchesPattern(path: string, pattern: string): boolean {
  if (!path || !pattern) return false

  if (pattern.endsWith('/*')) {
    const prefix = pattern.slice(0, -2)
    return path.startsWith(prefix)
  }
  return path === pattern
}
