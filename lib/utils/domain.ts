/**
 * Domain Utility Functions - Simplified
 *
 * Basic domain utilities for single-domain deployment.
 * Domain-specific routing has been removed.
 */

/**
 * Get the current domain from window.location (client-side only)
 * @returns Current hostname or empty string if not in browser
 */
export function getCurrentDomain(): string {
  if (typeof window === 'undefined') {
    return ''
  }
  return window.location.hostname
}
