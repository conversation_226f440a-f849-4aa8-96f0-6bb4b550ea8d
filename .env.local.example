# Local development environment variables
# Copy this file to .env.local and fill in the values
# DO NOT COMMIT .env.local to version control

# Environment
NODE_ENV=development

# Database - Set by the tunnel script (scripts/dev-tunnel.sh)
# DATABASE_URL=postgres://postgres:password@localhost:5432/website

# Redis - Set by the tunnel script (scripts/dev-tunnel.sh)
# REDIS_URL=redis://default:password@localhost:6380

# Authentication
JWT_SECRET=your-secret-key-at-least-32-characters-long

# Note: For development, run the tunnel script to set up database connections:
# npm run tunnel
