{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "tunnel": "bash scripts/dev-tunnel.sh", "tunnel:stop": "bash scripts/dev-tunnel.sh stop", "tunnel:status": "bash scripts/dev-tunnel.sh status", "tunnel:restart": "bash scripts/dev-tunnel.sh restart", "tunnel:clean": "bash scripts/dev-tunnel.sh clean", "dev:tunnel": "bash scripts/dev-tunnel.sh && next dev", "db:generate": "drizzle-kit generate", "db:migrate": "node -r dotenv/config drizzle/migrate.js", "db:studio": "drizzle-kit studio", "db:view": "node scripts/view-db.js", "db:seed:classes": "node scripts/seed-classes.js", "test:db": "node lib/test-db.js", "test:domain": "node lib/test-domain.js", "test:session": "node scripts/test-session-system.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/papaparse": "^5.3.16", "@types/ua-parser-js": "^0.7.39", "autoprefixer": "^10.4.20", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.30.5", "embla-carousel-react": "8.5.1", "file-saver": "^2.0.5", "input-otp": "1.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "jszip": "^3.10.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "node-fetch": "^3.3.2", "papaparse": "^5.5.3", "pg": "^8.16.0", "postgres": "^3.4.3", "puppeteer": "^24.8.2", "qrcode": "^1.5.4", "react": "^19", "react-day-picker": "^9.6.7", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-qr-code": "^2.0.15", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "^4.6.13", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uuid": "^9.0.1", "vaul": "^1.1.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/dotenv": "^6.1.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "drizzle-kit": "^0.21.2", "eslint": "^9.26.0", "eslint-config-next": "^15.3.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}