# ShalatYuk Environment Configuration Example
# Copy this file to .env.local for development or .env for production
# and update the values according to your setup

# =============================================================================
# ENVIRONMENT
# =============================================================================
NODE_ENV=development

# =============================================================================
# DOMAIN CONFIGURATION - SIMPLIFIED
# =============================================================================
# Domain configuration has been removed - application works on any domain
# No domain configuration needed - works with VPS default domains, EasyPanel, etc.

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database connection string
DATABASE_URL=postgres://postgres:password@localhost:5432/website

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis connection string for caching and sessions
REDIS_URL=redis://localhost:6379
# Optional Redis password if required
REDIS_PASSWORD=your_redis_password

# =============================================================================
# AUTHENTICATION
# =============================================================================
# JWT secret key for token signing (REQUIRED - generate a strong random key)
JWT_SECRET=your-secret-key-at-least-32-characters-long

# =============================================================================
# SCHOOL CONFIGURATION - FULLY CUSTOMIZABLE
# =============================================================================
# School information (customizable for different schools)
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=Your School Address
SCHOOL_WEBSITE=https://yourschool.edu

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# N8N WhatsApp webhook URL for OTP sending
N8N_WHATSAPP_WEBHOOK_URL=https://your-n8n-instance.com/webhook/your-webhook-id

