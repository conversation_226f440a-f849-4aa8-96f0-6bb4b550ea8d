# TODO: Enhanced Attendance Types and Role-Based Access Control

## Overview
Implement comprehensive attendance types (10 types) and role-based access control (5 roles) while maintaining production stability and clean architecture principles.

## Current State Analysis
- **Current Attendance Types**: 4 (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>jin) - ✅ **ENUM EXISTS**
- **Current Roles**: 3 (student, admin, super_admin) - ✅ **ENUM EXISTS**
- **Target Attendance Types**: 10 (as specified below)
- **Target Roles**: 5 (Super Admin, Admin, Teacher, Receptionist, Student)

## 🎯 Target Attendance Types

### School Attendance
1. **Entry** - Regular school entry
2. **Late Entry** - Late school entry
3. **Excused Absence** - Authorized absence from school (events, competitions, family matters)
4. **Temporary Leave** - Temporary leave (temporarily leaving school)
5. **Return from Leave** - Return from leave
6. **Sick** - Sick leave

### Prayer Attendance
7. **Zuhr** - Zuhr prayer (existing - keep unchanged)
8. **Asr** - Asr prayer (existing - keep unchanged)
9. **Ijin** - Prayer exemption (existing - keep unchanged)

### Departure
10. **Pulang** - School dismissal (existing - keep unchanged)

## 🔐 Role-Based Access Control

**Note**: Role configuration details have been moved to a separate refactoring plan. See `TODO-ROLE-CONFIG-REFACTOR.md` for comprehensive role-based access control implementation.

### Basic Role Requirements
- **Target Roles**: 5 (Super Admin, Admin, Teacher, Receptionist, Student)
- **Current Implementation**: Basic role system exists with `student`, `admin`, `super_admin`
- **Missing Roles**: `teacher`, `receptionist` need to be added to database enum

## 📋 Implementation Plan

### Phase 1: Database Schema Updates

#### 1.1 Update Attendance Type Enum
- [ ] **File**: `lib/data/drizzle/schema.ts`
- [ ] **Action**: Extend existing `attendanceTypeEnum` to include 6 new types (keeping existing 4)
- [ ] **Migration**: Create migration file for enum update
- [ ] **Backward Compatibility**: ✅ Existing enum confirmed - only ADD new values

```sql
-- Migration: Add new attendance types (keeping existing ones unchanged)
-- Best Practice: Use transaction and proper ordering for enum modifications
BEGIN;

-- Add new attendance types in logical order
-- School entry types
ALTER TYPE attendance_type ADD VALUE 'ENTRY';
ALTER TYPE attendance_type ADD VALUE 'LATE_ENTRY';

-- School absence/leave types
ALTER TYPE attendance_type ADD VALUE 'EXCUSED_ABSENCE';
ALTER TYPE attendance_type ADD VALUE 'SICK_LEAVE';
ALTER TYPE attendance_type ADD VALUE 'TEMPORARY_LEAVE';
ALTER TYPE attendance_type ADD VALUE 'RETURN_FROM_LEAVE';

-- Verify enum values were added successfully
SELECT unnest(enum_range(NULL::attendance_type)) AS attendance_types;

COMMIT;

-- Note: Keep existing 'Zuhr', 'Asr', 'Pulang', 'Ijin' unchanged to avoid breaking production
-- Best Practices Applied:
-- 1. Use UPPER_SNAKE_CASE for consistency with database conventions
-- 2. Wrap in transaction for atomicity
-- 3. Group related enum values logically
-- 4. Include verification query
-- 5. Use descriptive names (SICK_LEAVE instead of just SICK)
```

#### 1.2 Update User Role Enum
- [ ] **File**: `lib/data/drizzle/schema.ts`
- [ ] **Action**: Add `teacher` and `receptionist` to existing `userRoleEnum`
- [ ] **Migration**: Create migration file for role enum update
- [ ] **Constraint Update**: ✅ Existing enum confirmed - only ADD new values

```sql
-- Migration: Add new user roles
ALTER TYPE user_role ADD VALUE 'teacher';
ALTER TYPE user_role ADD VALUE 'receptionist';
```

#### 1.3 Update Database Constraints
- [ ] **File**: `lib/data/drizzle/schema.ts`
- [ ] **Action**: Update `chk_role_data` constraint to include new roles
- [ ] **Migration**: Update constraint validation logic

### Phase 2: Domain Layer Updates

#### 2.1 New Attendance Types
- [ ] **File**: `lib/types/attendance.ts`
- [ ] **Action**: Add new attendance types to enum
- [ ] **Action**: Update type definitions and interfaces

#### 2.2 Update Attendance Domain
- [ ] **File**: `lib/domain/entities/absence.ts`
- [ ] **Action**: Extend `AttendanceType` enum with all 10 types
- [ ] **Validation**: Add business rules for attendance type combinations

```typescript
export enum AttendanceType {
  // School Attendance (new)
  ENTRY = 'ENTRY',
  LATE_ENTRY = 'LATE_ENTRY',
  EXCUSED_ABSENCE = 'EXCUSED_ABSENCE',
  TEMPORARY_LEAVE = 'TEMPORARY_LEAVE',
  RETURN_FROM_LEAVE = 'RETURN_FROM_LEAVE',
  SICK_LEAVE = 'SICK_LEAVE',
  
  // Prayer Attendance (existing - keep unchanged)
  ZUHR = 'Zuhr',
  ASR = 'Asr',
  IJIN = 'Ijin',
  
  // Departure (existing - keep unchanged)
  PULANG = 'Pulang',
}

// Indonesian translations for UI display
export const AttendanceTypeLabels: Record<AttendanceType, string> = {
  [AttendanceType.ENTRY]: 'Masuk',
  [AttendanceType.LATE_ENTRY]: 'Masuk Terlambat',
  [AttendanceType.EXCUSED_ABSENCE]: 'Izin Tidak Masuk',
  [AttendanceType.TEMPORARY_LEAVE]: 'Izin Keluar Sementara',
  [AttendanceType.RETURN_FROM_LEAVE]: 'Kembali dari Izin',
  [AttendanceType.SICK_LEAVE]: 'Sakit',
  [AttendanceType.ZUHR]: 'Shalat Zuhur',
  [AttendanceType.ASR]: 'Shalat Ashar',
  [AttendanceType.IJIN]: 'Izin Shalat',
  [AttendanceType.PULANG]: 'Pulang',
};


```

#### 2.3 Update User Entities
- [ ] **File**: `lib/domain/entities/admin.ts`
- [ ] **Action**: Update to support new attendance types
- [ ] **File**: `lib/domain/entities/student.ts`
- [ ] **Action**: Ensure consistency with attendance type system

### Phase 3: Use Cases and Business Logic

#### 3.1 Attendance Use Cases
- [ ] **File**: `lib/domain/usecases/attendance/` (new directory)
- [ ] **Actions**:
  - [ ] `record-attendance.usecase.ts` - Handle attendance recording with new types
  - [ ] `validate-attendance-type.usecase.ts` - Validate attendance type business rules
  - [ ] `get-attendance-summary.usecase.ts` - Generate reports for new attendance types

### Phase 4: Data Layer Updates

#### 4.1 Repository Updates
- [ ] **File**: `lib/data/repositories/absence.repository.ts`
- [ ] **Action**: Update to handle new attendance types
- [ ] **Action**: Add queries for new attendance type filtering

#### 4.2 Cache Strategy
- [ ] **File**: `lib/data/cache/` (update existing)
- [ ] **Action**: Update caching strategy for attendance types
- [ ] **Redis Keys**: Add attendance type-based cache keys

### Phase 5: API and Frontend Updates

#### 5.1 API Routes Updates

- [ ] **File**: `app/api/absence/route.ts`
- [ ] **Action**: Add support for new attendance types
- [ ] **Implementation**: Update validation to handle all 10 attendance types

- [ ] **File**: `app/api/admin/scanner/route.ts`
- [ ] **Action**: Update scanner API to support new attendance types
- [ ] **Implementation**: Add attendance type filtering and validation

#### 5.2 Frontend Component Updates

- [ ] **File**: `components/scanner/AttendanceTypeSelector.tsx`
- [ ] **Action**: Update to display all new attendance types
- [ ] **Implementation**: Add UI for new attendance type selection

- [ ] **File**: `components/reports/AttendanceReport.tsx`
- [ ] **Action**: Update reports to include new attendance types
- [ ] **Implementation**: Add filtering and display for new types

### Phase 6: Testing and Validation

#### 6.1 Unit Tests

- [ ] **File**: `__tests__/domain/attendance-types.test.ts` (new)
- [ ] **Action**: Test attendance type validation and logic
- [ ] **Coverage**: All attendance type enums and validation functions

- [ ] **File**: `__tests__/api/attendance.test.ts`
- [ ] **Action**: Test attendance API with new types
- [ ] **Coverage**: Create, read, update operations for all attendance types

#### 6.2 Integration Tests

- [ ] **File**: `__tests__/integration/attendance-flow.test.ts` (new)
- [ ] **Action**: Test end-to-end attendance recording
- [ ] **Coverage**: Scanner integration, database persistence, reporting

#### 6.3 Manual Testing Checklist

- [ ] **Attendance Recording**: Test all 10 attendance types can be recorded
- [ ] **Scanner Integration**: Verify scanner works with new attendance types
- [ ] **Reports**: Ensure reports display all attendance types correctly
- [ ] **Data Migration**: Verify existing data remains intact
- [ ] **Performance**: Check query performance with new enum values

### Phase 7: Migration and Deployment

#### 7.1 Database Migration

- [ ] **File**: `lib/data/drizzle/migrations/add-attendance-types.sql` (new)
- [ ] **Action**: Add new attendance types to enum

```sql
-- Migration: Add new attendance types
ALTER TYPE attendance_type ADD VALUE 'dzuhur';
ALTER TYPE attendance_type ADD VALUE 'ashar';
ALTER TYPE attendance_type ADD VALUE 'maghrib';
ALTER TYPE attendance_type ADD VALUE 'isya';
ALTER TYPE attendance_type ADD VALUE 'tahajud';
ALTER TYPE attendance_type ADD VALUE 'dhuha';
ALTER TYPE attendance_type ADD VALUE 'tarawih';
ALTER TYPE attendance_type ADD VALUE 'tahfidz';
```

#### 7.2 Deployment Steps

1. [ ] **Pre-deployment**: Backup current database
2. [ ] **Deploy Code**: Deploy new attendance type system
3. [ ] **Run Migrations**: Execute database migrations for new types
4. [ ] **Verify Deployment**: Test all attendance type scenarios
5. [ ] **Monitor**: Watch for enum-related errors in logs

### Phase 8: Documentation

#### 8.1 Technical Documentation

- [ ] **File**: `docs/attendance-types.md` (new)
- [ ] **Content**: Complete attendance type system documentation
- [ ] **Sections**: Types overview, usage examples, migration guide

#### 8.2 API Documentation

- [ ] **File**: `docs/api/attendance-endpoints.md`
- [ ] **Action**: Update API documentation for new attendance types
- [ ] **Format**: Include all 10 attendance types in examples

## Implementation Priority

### High Priority (Week 1)
1. ✅ Database schema updates
2. ✅ Domain layer updates for attendance types
3. ⏳ Use case updates
4. ⏳ Data layer updates

### Medium Priority (Week 2)
1. ⏳ API route updates
2. ⏳ Frontend component updates
3. ⏳ Database migrations
4. ⏳ Basic testing implementation

### Low Priority (Week 3)
1. ⏳ Comprehensive testing suite
2. ⏳ Documentation updates
3. ⏳ Performance optimization
4. ⏳ Advanced reporting features

## Success Metrics

- [ ] **Functionality**: All 10 attendance types can be recorded and tracked
- [ ] **Performance**: No significant impact on database query performance
- [ ] **Data Integrity**: Existing attendance data remains intact
- [ ] **User Experience**: Seamless attendance recording with new types
- [ ] **Reporting**: Comprehensive reports include all attendance types

---

**Note**: This implementation focuses on expanding attendance types while maintaining system performance and data integrity.

## 🔒 Security Considerations

### Data Validation
- [ ] **Input Validation**: Strict validation for new attendance types
- [ ] **Enum Constraints**: Ensure only valid attendance types are accepted
- [ ] **Audit Logging**: Log all attendance recordings with type information

## 📊 Performance Considerations

### Database Optimization
- [ ] **Indexes**: Add indexes for attendance type queries
- [ ] **Query Performance**: Monitor enum-based filtering performance
- [ ] **Materialized Views**: Update attendance summary views
- [ ] **Query Optimization**: Optimize attendance type filtering queries

### Caching Strategy
- [ ] **Attendance Types**: Cache attendance type metadata
- [ ] **Cache Invalidation**: Implement proper cache invalidation for attendance data

## 🧪 Testing Strategy

### Test Coverage Requirements
- [ ] **Unit Tests**: 90%+ coverage for attendance type logic
- [ ] **Integration Tests**: All attendance API endpoints
- [ ] **E2E Tests**: Complete attendance recording workflows

### Test Data
- [ ] **Fixtures**: Create test data for all attendance types
- [ ] **Scenarios**: Test edge cases and validation boundaries
- [ ] **Performance**: Load testing for attendance queries

## 📝 Documentation Updates

### Technical Documentation
- [ ] **API Documentation**: Update OpenAPI specs with new attendance types
- [ ] **Database Schema**: Document attendance type enum changes
- [ ] **Architecture**: Update attendance system documentation
- [ ] **Localization Guide**: Document the dual-language approach (English code, Indonesian UI)

### User Documentation
- [ ] **Attendance Types**: Document when to use each attendance type (in Indonesian)
- [ ] **User Manual**: Update user manual with new attendance features (in Indonesian)
- [ ] **Translation Reference**: Maintain mapping between English enum values and Indonesian labels

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] **Code Review**: Comprehensive review of all changes
- [ ] **Testing**: All tests passing in staging environment
- [ ] **Performance**: Performance testing completed
- [ ] **Migration Testing**: Test database migration thoroughly

### Deployment
- [ ] **Database Migration**: Run attendance type migrations in production
- [ ] **Application Deployment**: Deploy new application version
- [ ] **Verification**: Verify all attendance types working correctly
- [ ] **Monitoring**: Monitor system performance and errors

### Post-Deployment
- [ ] **User Training**: Train users on new attendance types
- [ ] **Documentation**: Update user-facing documentation
- [ ] **Feedback**: Collect and address user feedback
- [ ] **Optimization**: Monitor and optimize performance

## 🎯 Success Criteria

### Functional Requirements
- [ ] All 10 attendance types implemented and working
- [ ] Attendance recording works for all new types
- [ ] Reports include all attendance types
- [ ] Scanner integration supports new types

### Non-Functional Requirements
- [ ] No performance degradation from current system
- [ ] Zero downtime deployment achieved
- [ ] All existing functionality preserved
- [ ] Data integrity maintained

### User Acceptance
- [ ] Users can successfully record all attendance types
- [ ] Reports show correct data for all types
- [ ] No user complaints about missing functionality
- [ ] Smooth transition from existing system

## 📅 Timeline Estimate

- **Phase 1-2 (Database & Domain)**: 1 week
- **Phase 3-4 (Use Cases & Data)**: 1 week  
- **Phase 5-6 (API & Frontend)**: 2 weeks
- **Phase 7-8 (Testing & Documentation)**: 1 week

**Total Estimated Time**: 5 weeks

## 🚨 Risk Mitigation

### Technical Risks
- **Database Migration**: 
  - Test thoroughly in staging environment
  - **PostgreSQL Enum Limitation**: Cannot remove enum values once added
  - Use feature flags to control new attendance type usage
  - Implement gradual rollout strategy
- **Performance**: Monitor query performance with new indexes
- **Naming Consistency**: Ensure database enum values match TypeScript enum values

### Business Risks
- **User Confusion**: Provide clear documentation and training
- **Data Loss**: Implement comprehensive backup strategy
- **Downtime**: Use blue-green deployment strategy
- **Enum Value Conflicts**: Establish clear naming conventions and validation

### Migration-Specific Risks
- **Irreversible Changes**: PostgreSQL enum additions cannot be rolled back
- **Application Compatibility**: Ensure old application versions can handle new enum values
- **Data Integrity**: Validate all existing data remains valid after migration

## 📞 Support Plan

### During Implementation
- **Daily Standups**: Track progress and blockers
- **Code Reviews**: Ensure quality and consistency
- **Testing**: Continuous testing throughout development

### Post-Deployment
- **Monitoring**: 24/7 monitoring for first week
- **Support**: Dedicated support team for user issues
- **Hotfixes**: Rapid response plan for critical issues

---

**Note**: This implementation focuses on expanding attendance types while maintaining system stability and data integrity. All changes are backward compatible and include proper migration paths.