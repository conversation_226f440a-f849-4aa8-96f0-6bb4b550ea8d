# ShalatYuk

ShalatYuk adalah aplikasi absensi shalat dan pulang sekolah untuk siswa SMA Banjarmasin.

## Fitur

- Login siswa dan admin dengan username/password
- Veri<PERSON><PERSON>i WhatsApp dengan OTP
- QR Code unik untuk absensi
- A<PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan <PERSON>
- <PERSON><PERSON><PERSON> absensi
- <PERSON><PERSON><PERSON><PERSON> pengguna
- Role Admin dan Super Admin dengan hak akses berbeda
- Session management dengan single device login

## Teknologi

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Shadcn UI
- PostgreSQL dengan Drizzle ORM
- Redis untuk caching dan session management
- JWT untuk autentikasi
- Clean Architecture

## Prasyarat

- Node.js 18+
- PostgreSQL (diakses melalui SSH tunnel)
- Redis (diakses melalui SSH tunnel)
- n8n (opsional, untuk webhook WhatsApp)
- SSH key untuk koneksi ke server

## Instalasi

1. Clone repositori:

```bash
git clone https://github.com/firdaus1453/ShalatYuk.git
cd ShalatYuk
```

2. Instal dependensi:

```bash
npm install
```

3. Salin file `.env.local.example` ke `.env.local`:

```bash
cp .env.local.example .env.local
```

Catatan: Anda tidak perlu mengubah nilai DATABASE_URL dan REDIS_URL secara manual, karena akan diatur oleh script tunnel.

4. Siapkan SSH tunnel untuk PostgreSQL dan Redis menggunakan script yang telah disediakan:

```bash
# Memulai tunnel
npm run tunnel

# Atau langsung dengan development server
npm run dev:tunnel
```

Script ini akan otomatis membuat koneksi SSH tunnel ke PostgreSQL dan Redis, serta memperbarui file .env.local dengan konfigurasi yang benar.

5. Generate dan jalankan migrasi Drizzle:

```bash
npm run db:generate
npm run db:migrate
```

6. Jalankan aplikasi:

```bash
npm run dev
```

7. Buka [http://localhost:3000](http://localhost:3000) di browser.

## Struktur Proyek

```
ShalatYuk/
├── app/                  # Next.js App Router
│   ├── api/              # API Routes
│   ├── admin/            # Admin App
│   ├── student/          # Student App
│   └── page.tsx          # Landing Page
├── components/           # Reusable UI components
├── lib/                  # Library code
│   ├── domain/           # Domain layer (entities, use cases)
│   ├── data/             # Data layer (repositories, cache)
│   ├── config.ts         # Centralized configuration
│   └── mock-data.ts      # Mock data for development
├── public/               # Static assets
├── drizzle/              # Drizzle migrations
├── scripts/              # Utility scripts
│   └── dev-tunnel.sh     # SSH tunnel script for development
├── .env                  # Default environment variables (for production)
├── .env.local            # Local environment variables (not committed)
└── middleware.ts         # Next.js middleware
```

## Konfigurasi Lingkungan

Aplikasi ini menggunakan dua file untuk konfigurasi lingkungan:

1. `.env` - Berisi nilai default untuk lingkungan production
2. `.env.local` - Berisi nilai khusus untuk lingkungan development (tidak di-commit ke git)

Untuk development, gunakan script tunnel untuk mengatur koneksi database:

```bash
npm run tunnel
```

Script ini akan otomatis mengatur DATABASE_URL dan REDIS_URL di file `.env.local`.

## Pengembangan

### Database

- Lihat skema database: `npm run db:studio`
- Generate migrasi: `npm run db:generate`
- Jalankan migrasi: `npm run db:migrate`

### Autentikasi

- Siswa: Google OAuth
- Admin: Username/password

### Role Admin

ShalatYuk memiliki dua jenis role admin dengan hak akses berbeda:

1. **Admin**: Hanya dapat mengakses fitur Scanner dan Laporan
2. **Super Admin**: Memiliki akses penuh ke semua fitur, termasuk Manajemen User dan Manajemen Kelas

Untuk informasi lebih lanjut tentang pengelolaan role admin, lihat [Panduan Super Admin](docs/super-admin-guide.md).

### Absensi

- Siswa: Scan QR Code unik
- Admin: Scan QR Code siswa atau input kode unik manual

### Testing

- Test koneksi database: `npm run test:db`
- Test domain layer: `npm run test:domain`

Pastikan SSH tunnel sudah berjalan sebelum menjalankan test koneksi database.

## Logging

ShalatYuk implements a robust logging system using Winston for tracking application events:

### Features

- **Comprehensive Logging**: API requests/responses, authentication events, system events, and warnings are all logged
- **Log Rotation**: Daily log files with automatic rotation to prevent excessive disk usage
- **Structured Logging**: JSON format for easy parsing and analysis
- **Sensitive Data Filtering**: Passwords and tokens are automatically removed from logs

### Accessing Logs

Logs can be accessed by administrators through the following API endpoint (no UI component):

```
GET /api/admin/logs
```

#### Query Parameters

- `date`: Format YYYY-MM-DD (defaults to today)
- `level`: Filter by log level - 'info', 'warn', 'error'
- `type`: Filter by event type - 'api', 'auth', 'system', 'warning'
- `limit`: Number of entries to return (default 100, max 1000)
- `page`: Page number for pagination (default 1)

#### Example

```
GET /api/admin/logs?date=2023-06-15&level=error&limit=50&page=1
```

### Deleting Logs

Administrators can delete log files for specific dates:

```
DELETE /api/admin/logs?date=2023-06-15
```

### Log File Location

Log files are stored in the `logs` directory in the project root with the naming pattern `shalat-yuk-YYYY-MM-DD.log`.

## Lisensi

Hak Cipta © 2025 SMA Banjarmasin
