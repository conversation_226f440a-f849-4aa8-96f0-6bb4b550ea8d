# Test User Credentials for Role-Based Testing
# Generated on: 2025-06-10T06:52:31.444Z

## Student Account
Username: teststudent
Password: password123
Role: student
Access: /student/home, /student/profile only

## Admin Account  
Username: testadmin
Password: password123
Role: admin
Access: /admin/home (scanner), /admin/reports, /admin/profile only

## Super Admin Account
Username: testsuperadmin  
Password: password123
Role: super_admin
Access: All /admin/* pages

## Testing Instructions

1. Login as each user type at:
   - Student: http://localhost:3000/student
   - Admin: http://localhost:3000/admin
   - Super Admin: http://localhost:3000/admin

2. Verify navigation menus show correct items for each role

3. Test page access restrictions:
   - Students should be redirected if accessing /admin/* pages
   - Admins should be redirected if accessing /admin/users, /admin/admins, etc.
   - Super admins should have access to all pages

4. Test attendance type restrictions in scanner:
   - <PERSON><PERSON> can only record: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ng, <PERSON>jin
   - <PERSON> admins can record all types

## Security Testing

Try accessing unauthorized URLs directly:
- As student: http://localhost:3000/admin/home (should redirect)
- As admin: http://localhost:3000/admin/users (should redirect)
- As admin: http://localhost:3000/admin/admins (should redirect)
