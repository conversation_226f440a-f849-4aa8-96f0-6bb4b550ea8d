import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/data/drizzle/db'
import postgres from 'postgres'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

/**
 * GET /api/check-constraint
 * Endpoint untuk mendapatkan informasi tentang constraint database
 */
export async function GET(req: NextRequest) {
  try {
    // Query untuk melihat informasi constraint menggunakan raw SQL via db client
    const sql = db as unknown as postgres.Sql

    const constraintInfo = await sql`
      SELECT conname, contype, pg_get_constraintdef(oid) as condef
      FROM pg_constraint
      WHERE conname = 'chk_role_data'
    `

    // Query untuk melihat schema tabel users
    const tableInfo = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `

    // Check beberapa data student di database
    const studentSample = await sql`
      SELECT id, role, unique_code, google_email, username, password_hash
      FROM users
      WHERE role = 'student'
      LIMIT 2
    `

    return NextResponse.json({
      constraint: constraintInfo,
      table: tableInfo,
      sample: studentSample,
    })
  } catch (error) {
    console.error('Error checking constraint:', error)
    return NextResponse.json(
      { message: 'Failed to check constraint', error: String(error) },
      { status: 500 }
    )
  }
}
