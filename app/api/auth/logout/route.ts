import { NextRequest, NextResponse } from 'next/server'
import { authenticate } from '@/lib/middleware/auth'
import { authenticateWithSession, logoutUser } from '@/lib/middleware/enhanced-auth'
import { getRedisCache } from '@/lib/data/cache/redis'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

const cache = getRedisCache()

/**
 * POST /api/auth/logout
 * Logs out the user by invalidating their tokens and sessions
 */
export async function POST(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const role = url.searchParams.get('role')

    // Try enhanced authentication first (with session management)
    try {
      const authResult = await authenticateWithSession(req)

      // Create response
      const response = NextResponse.json({
        success: true,
        message: 'Logged out successfully',
      })

      // Use enhanced logout (invalidates session and clears cookies)
      const userRole = authResult.role === 'student' ? 'student' : 'admin'
      return await logout<PERSON><PERSON>(response, authResult.sessionId, authResult.id, userRole)
    } catch (enhancedAuthError) {
      console.log('Enhanced authentication failed, falling back to legacy logout')

      // Fallback to legacy authentication
      try {
        const decoded = await authenticate(req, role as any)

        // If authentication succeeds, try to invalidate the refresh token in Redis
        try {
          await cache.del(`auth:refresh:${decoded.id}`)
          console.log(`Successfully invalidated refresh token for user ${decoded.id}`)
        } catch (cacheError) {
          console.error('Failed to invalidate refresh token in Redis:', cacheError)
          // Continue with cookie clearing even if Redis operation fails
        }
      } catch (error) {
        // If authentication fails, just continue with cookie clearing
        console.log('Authentication failed during logout, continuing with cookie clearing')
      }
    }

    // Create a response that clears the auth cookies
    const response = NextResponse.json({ success: true })

    if (!role || role === 'admin' || role === 'super_admin') {
      // Clear the admin auth tokens
      response.cookies.set('admin_auth_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0, // Expire immediately
        path: '/',
      })

      response.cookies.set('admin_refresh_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0, // Expire immediately
        path: '/',
      })
    }

    if (!role || role === 'student') {
      // Clear the student auth tokens
      response.cookies.set('student_auth_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0, // Expire immediately
        path: '/',
      })

      response.cookies.set('student_refresh_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0, // Expire immediately
        path: '/',
      })
    }

    // For backward compatibility, clear any old auth tokens too
    response.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // Expire immediately
      path: '/',
    })

    response.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // Expire immediately
      path: '/',
    })

    return response
  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json({ error: 'Failed to logout' }, { status: 500 })
  }
}
