import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { hashPassword } from '@/lib/utils/auth'
import { Admin, UpdateAdminDTO } from '@/lib/domain/entities/admin'
import { Student, UpdateStudentDTO } from '@/lib/domain/entities/student'
import { NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Schema untuk body request PATCH admin
const apiAdminUpdateSchema = z.object({
  role: z.enum(['admin', 'super_admin']),
  name: z.string().min(1, 'Nama wajib diisi').optional(),
  username: z.string().min(3, 'Username minimal 3 karakter').optional(), // Assuming username can be updated
  password: z.string().min(6, 'Password minimal 6 karakter').optional(),
})

// Schema untuk body request PATCH student
const apiStudentUpdateSchema = z.object({
  role: z.literal('student'),
  name: z.string().min(1, 'Nama wajib diisi').optional(),
  nis: z.string().nullable().optional(),
  whatsapp: z.string().nullable().optional(),
  classId: z.coerce.number().int().positive('ID Kelas harus angka positif').nullable().optional(),
  password: z.string().min(6, 'Password minimal 6 karakter').optional(), // For Super Admin changing student password
})

const updateUserApiSchema = z.discriminatedUnion('role', [
  apiAdminUpdateSchema,
  apiStudentUpdateSchema,
])

/**
 * PATCH /api/users/[id]
 * Update a user (student or admin)
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin and get admin data for authorization checks
    let adminId: number
    try {
      adminId = await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Get the full admin data to check role (use admin repository directly)
    console.info(`Fetching admin data for ID: ${adminId}`)
    const adminData = await adminRepo.findById(adminId)
    console.info(`Admin data retrieved:`, {
      found: !!adminData,
      id: adminData?.id,
      role: adminData?.role,
      username: adminData?.username,
    })

    if (!adminData || (adminData.role !== 'admin' && adminData.role !== 'super_admin')) {
      console.error(`Admin validation failed:`, {
        adminFound: !!adminData,
        adminRole: adminData?.role,
        isValidRole: adminData?.role === 'admin' || adminData?.role === 'super_admin',
      })
      return NextResponse.json({ error: 'Admin not found or invalid role' }, { status: 403 })
    }

    // First await the params object to get id
    const { id } = await params
    const userId = parseInt(id)

    if (isNaN(userId)) {
      return NextResponse.json({ message: 'ID User tidak valid' }, { status: 400 })
    }

    // Parse the request body
    const body = await req.json()

    // Validate the request body
    const validation = updateUserApiSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validation.data
    let updatedUserResult: Student | Admin | null = null

    if (validatedData.role === 'admin' || validatedData.role === 'super_admin') {
      const { role, ...adminPayload } = validatedData
      const adminUpdateDto: UpdateAdminDTO = {}
      if (adminPayload.name) adminUpdateDto.name = adminPayload.name
      if (adminPayload.username) (adminUpdateDto as any).username = adminPayload.username
      if (adminPayload.password) {
        adminUpdateDto.password = adminPayload.password
      }
      updatedUserResult = await userUseCases.updateAdmin(userId, adminUpdateDto)
    } else if (validatedData.role === 'student') {
      const { role, ...studentPayload } = validatedData

      // Log the payload for debugging (without logging password)
      console.info(`Updating student ${userId} with payload:`, {
        ...studentPayload,
        password: studentPayload.password ? '(provided)' : '(not provided)',
      })

      try {
        // First update the student's basic info (classId, whatsapp, nis)
        const studentDTO: UpdateStudentDTO = {
          classId: studentPayload.classId,
          nis: studentPayload.nis,
          whatsapp: studentPayload.whatsapp,
        }

        // Add name to DTO if provided
        if (studentPayload.name) {
          studentDTO.name = studentPayload.name
        }

        // Update student basic info
        updatedUserResult = await userUseCases.updateStudent(userId, studentDTO)

        // Handle password change separately if provided (Super Admin only)
        if (studentPayload.password) {
          // Debug logging
          console.info(`Password change requested by admin:`, {
            adminId: adminData.id,
            adminRole: adminData.role,
            adminRoleType: typeof adminData.role,
            isSuper: adminData.role === 'super_admin',
          })

          // Verify that the current admin is a Super Admin
          if (adminData.role !== 'super_admin') {
            console.error(`Access denied for admin role: ${adminData.role}`)
            return NextResponse.json(
              { message: 'Only Super Admin can change student passwords' },
              { status: 403 }
            )
          }

          // Use the domain use case to change the student's password
          await userUseCases.changeStudentPasswordByAdmin(userId, studentPayload.password, adminId)
          console.info(`Password changed for student ${userId} by Super Admin ${adminId}`)
        }
      } catch (error) {
        console.error(`Error updating student ${userId}:`, error)
        throw error // Re-throw to be caught by outer catch
      }
    } else {
      // Should not be reached due to discriminated union
      return NextResponse.json({ message: 'Role tidak valid untuk update' }, { status: 400 })
    }

    if (!updatedUserResult) {
      return NextResponse.json(
        { message: 'User tidak ditemukan atau gagal update' },
        { status: 404 }
      )
    }

    console.info(`User ${userId} updated successfully.`)
    await cache.del('users:all')

    // Return the updated user, ensuring properties are accessed safely based on role
    if (updatedUserResult.role === 'admin' || updatedUserResult.role === 'super_admin') {
      const admin = updatedUserResult as Admin
      return NextResponse.json({
        id: admin.id,
        name: admin.name,
        username: admin.username,
        role: admin.role,
      })
    } else {
      const student = updatedUserResult as Student
      return NextResponse.json({
        id: student.id,
        name: student.name,
        username: student.username,
        uniqueCode: student.uniqueCode,
        googleEmail: student.googleEmail,
        nis: student.nis,
        className: student.className,
        role: student.role,
        classId: student.classId,
      })
    }
  } catch (error) {
    console.error(`Failed to update user:`, error instanceof Error ? error.stack : error)
    const errorMessage = error instanceof Error ? error.message : 'Gagal memperbarui data user'
    if (errorMessage.toLowerCase().includes('not found')) {
      return NextResponse.json({ message: 'User tidak ditemukan' }, { status: 404 })
    }
    if (
      errorMessage.toLowerCase().includes('unique constraint') ||
      errorMessage.toLowerCase().includes('violates unique constraint users_username_key')
    ) {
      return NextResponse.json(
        { message: 'Username sudah digunakan.', error: errorMessage },
        { status: 409 }
      )
    }
    // Add specific error handling for class-related issues
    if (
      errorMessage.toLowerCase().includes('class') ||
      errorMessage.toLowerCase().includes('kelas')
    ) {
      return NextResponse.json(
        { message: 'Error terkait kelas. Pastikan kelas yang dipilih valid.', error: errorMessage },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { message: 'Gagal memperbarui data user', error: errorMessage },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/users/[id]
 * Delete a user (student or admin)
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // First await the params object to get id
    const { id } = await params
    const userId = parseInt(id)

    if (isNaN(userId)) {
      return NextResponse.json({ message: 'ID User tidak valid' }, { status: 400 })
    }

    // Fetch the user first to determine role
    const userToDelete = await userUseCases.getUserById(userId)

    if (!userToDelete) {
      return NextResponse.json({ message: 'User tidak ditemukan' }, { status: 404 })
    }

    try {
      // Delete the user and related records
      await userUseCases.deleteUser(userId, userToDelete.role)

      console.info(`User ${userId} (${userToDelete.role}) deleted successfully.`)
      await cache.del('users:all')

      return NextResponse.json({ message: 'User berhasil dihapus' }, { status: 200 })
    } catch (error: any) {
      console.error(`Error deleting user ${userId}:`, error)

      if (error instanceof NotFoundError) {
        return NextResponse.json({ message: error.message }, { status: 404 })
      }

      return NextResponse.json(
        {
          message: 'Gagal menghapus user',
          error: error.message,
        },
        { status: 500 }
      )
    }
  } catch (error: any) {
    console.error('Unexpected error in DELETE /api/users/[id]:', error)
    return NextResponse.json(
      { message: 'Terjadi kesalahan', error: error.message },
      { status: 500 }
    )
  }
}
