import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { db, schema } from '@/lib/data/drizzle/db'
import { sql, count, and, eq, gte, lt } from 'drizzle-orm'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

/**
 * GET /api/absence/counts
 * Get attendance counts for today
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Get today's date
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Convert dates to ISO strings for SQL
    const todayStr = today.toISOString()
    const tomorrowStr = tomorrow.toISOString()

    // Get counts for each attendance type
    const zuhrCount = await db
      .select({ count: count() })
      .from(schema.absences)
      .where(
        and(
          eq(schema.absences.type, 'Zuhr'),
          gte(schema.absences.recordedAt, new Date(todayStr)),
          lt(schema.absences.recordedAt, new Date(tomorrowStr))
        )
      )

    const asrCount = await db
      .select({ count: count() })
      .from(schema.absences)
      .where(
        and(
          eq(schema.absences.type, 'Asr'),
          gte(schema.absences.recordedAt, new Date(todayStr)),
          lt(schema.absences.recordedAt, new Date(tomorrowStr))
        )
      )

    const pulangCount = await db
      .select({ count: count() })
      .from(schema.absences)
      .where(
        and(
          eq(schema.absences.type, 'Pulang'),
          gte(schema.absences.recordedAt, new Date(todayStr)),
          lt(schema.absences.recordedAt, new Date(tomorrowStr))
        )
      )

    // Get total unique students who attended today
    const totalStudents = await db
      .select({ count: count(sql`DISTINCT ${schema.absences.uniqueCode}`) })
      .from(schema.absences)
      .where(
        and(
          gte(schema.absences.recordedAt, new Date(todayStr)),
          lt(schema.absences.recordedAt, new Date(tomorrowStr))
        )
      )

    return NextResponse.json({
      zuhr: zuhrCount[0].count,
      asr: asrCount[0].count,
      pulang: pulangCount[0].count,
      total: totalStudents[0].count,
    })
  } catch (error) {
    console.error('Error getting attendance counts:', error)
    return NextResponse.json({ error: 'Failed to get attendance counts' }, { status: 500 })
  }
}
