import { NextRequest, NextResponse } from 'next/server'
import { db, pgClient } from '@/lib/data/drizzle'
import { users, classes, absences } from '@/lib/data/drizzle/schema'
import { sql } from 'drizzle-orm'
import { serverConfig } from '@/lib/config'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

/**
 * GET /api/debug/db
 * Debug endpoint to view database tables and data
 * This should be disabled in production
 */
export async function GET(req: NextRequest) {
  // Check if we're in development mode
  if (serverConfig.environment.isProduction) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 403 }
    )
  }

  try {
    // Get counts for each table
    const userCount = await db.select({ count: sql<number>`count(*)` }).from(users)
    const classCount = await db.select({ count: sql<number>`count(*)` }).from(classes)
    const absenceCount = await db.select({ count: sql<number>`count(*)` }).from(absences)

    // Get sample data (limited to 5 rows per table)
    const userSample = await db.select().from(users).limit(5)
    const classSample = await db.select().from(classes).limit(5)
    const absenceSample = await db.select().from(absences).limit(5)

    // Get attendance summary data
    const attendanceSummary = await pgClient`
      SELECT * FROM attendance_summary LIMIT 5
    `

    // Get database information
    const dbInfo = await pgClient`
      SELECT current_database() as database_name, 
             current_user as username,
             version() as version
    `

    return NextResponse.json({
      database: dbInfo[0],
      connectionString: serverConfig.database.url?.replace(/:[^:]*@/, ':****@'),
      tables: {
        users: {
          count: userCount[0].count,
          sample: userSample,
        },
        classes: {
          count: classCount[0].count,
          sample: classSample,
        },
        absences: {
          count: absenceCount[0].count,
          sample: absenceSample,
        },
      },
      materializedViews: {
        attendanceSummary: {
          sample: attendanceSummary,
        },
      },
    })
  } catch (error) {
    console.error('Error fetching database info:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch database info',
        message: error instanceof Error ? error.message : String(error),
        connectionString: serverConfig.database.url?.replace(/:[^:]*@/, ':****@'),
      },
      { status: 500 }
    )
  }
}
