'use client'

import { useState, useEffect, Suspense, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  QrCode,
  Loader2,
  AlertTriangle,
  Download,
} from 'lucide-react'
import QRCode from 'react-qr-code'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import { getCurrentDateTime } from '@/lib/utils/date'
import { StudentAuthCheck } from '@/components/auth/student-auth-check'

interface Student {
  id: number
  uniqueCode: string
  googleEmail: string
  nis: string | null
  name: string
  whatsapp: string | null
  classId: number | null
  className?: string
  createdAt: string
  updatedAt: string | null
}

// Loading fallback for Suspense
function LoadingFallback() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-12 w-12 animate-spin text-indigo-600" />
        <p className="text-slate-600 dark:text-slate-300">Memuat data...</p>
      </div>
    </div>
  )
}

// Create a component that uses session to be wrapped in Suspense
function StudentHomeContent() {
  const router = useRouter()
  const { toast } = useToast()
  const [currentTime, setCurrentTime] = useState('')
  const [currentDate, setCurrentDate] = useState('')
  const [student, setStudent] = useState<Student | null>(null)
  const [loading, setLoading] = useState(true)
  const [profileLoadFailed, setProfileLoadFailed] = useState(false)
  const [loadRetries, setLoadRetries] = useState(0)
  const MAX_RETRIES = 3
  const qrCodeRef = useRef<HTMLDivElement>(null)

  // Logout function
  const handleLogout = async () => {
    try {
      setLoading(true)
      // Call the server logout endpoint to clear Redis tokens
      const response = await fetch('/api/auth/logout?role=student', {
        method: 'POST',
        credentials: 'include', // Important to include cookies
      })

      if (!response.ok) {
        throw new Error('Logout failed')
      }

      toast({
        title: 'Logged Out',
        description: 'You have been logged out successfully',
      })
      router.push('/student')
    } catch (error) {
      console.error('Logout error:', error)
      toast({
        title: 'Error',
        description: 'Failed to logout. Please try again.',
        variant: 'destructive',
      })
      setLoading(false)
    }
  }

  // Fetch student profile
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/student/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
          credentials: 'include', // Important: include credentials (cookies)
        })

        if (!response.ok) {
          if (response.status === 401 || response.status === 403) {
            console.error(`Authentication error: ${response.status}`)
            toast({
              title: 'Sesi berakhir',
              description: 'Silakan login kembali',
              variant: 'destructive',
            })
            // Sign out the user and redirect
            await handleLogout()
            router.push('/student')
            return
          }
          throw new Error(`Error fetching profile: ${response.statusText}`)
        }

        const data = await response.json()

        setStudent(data.student)
        setProfileLoadFailed(false)
      } catch (error) {
        console.error('Failed to load student profile:', error)
        setProfileLoadFailed(true)

        // Only show error toast if we've retried a few times
        if (loadRetries >= MAX_RETRIES - 1) {
          toast({
            title: 'Gagal memuat profil',
            description: 'Terjadi kesalahan saat memuat data profil',
            variant: 'destructive',
          })
        }
      } finally {
        setLoading(false)
      }
    }

    if (loading) {
      fetchProfile()
    }
  }, [router, toast, loadRetries])

  // Update date and time
  useEffect(() => {
    // Use the utility function to get current date and time
    const updateDateTime = () => {
      const { date, time } = getCurrentDateTime()
      setCurrentDate(date)
      setCurrentTime(time)
    }

    updateDateTime()
    const interval = setInterval(updateDateTime, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  // State for attendance status
  const [attendanceStatus, setAttendanceStatus] = useState({
    zuhur: false,
    zuhurTime: null as string | null,
    asr: false,
    asrTime: null as string | null,
    dismissal: false,
    dismissalTime: null as string | null,
  })

  // State for loading attendance status
  const [loadingAttendance, setLoadingAttendance] = useState(false)

  // Fetch attendance status
  useEffect(() => {
    const fetchAttendanceStatus = async () => {
      if (!student) return

      setLoadingAttendance(true)

      try {
        const response = await fetch(`/api/absence/check?uniqueCode=${student.uniqueCode}`)

        if (!response.ok) {
          console.error('Failed to fetch attendance status:', response.status)
          return
        }

        const data = await response.json()

        // Update attendance status based on API response
        setAttendanceStatus({
          zuhur: data.zuhr || false,
          zuhurTime: data.zuhrTime || null,
          asr: data.asr || false,
          asrTime: data.asrTime || null,
          dismissal: data.pulang || false,
          dismissalTime: data.pulangTime || null,
        })
      } catch (error) {
        console.error('Error fetching attendance status:', error)
        toast({
          title: 'Error',
          description: 'Gagal memuat status absensi',
          variant: 'destructive',
        })
      } finally {
        setLoadingAttendance(false)
      }
    }

    // Fetch initially
    fetchAttendanceStatus()

    // Set up polling every 30 seconds
    const interval = setInterval(fetchAttendanceStatus, 30000)

    return () => clearInterval(interval)
  }, [student, student?.uniqueCode, toast])

  // Function to handle QR Code download
  const handleDownloadQRCode = () => {
    if (qrCodeRef.current && student) {
      const svgElement = qrCodeRef.current.querySelector('svg')
      if (!svgElement) {
        toast({
          title: 'Error',
          description: 'Gagal menemukan elemen QR Code untuk diunduh.',
          variant: 'destructive',
        })
        return
      }

      const svgString = new XMLSerializer().serializeToString(svgElement)
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      const originalSize = 256 // Assuming default QRCode size used in component is around this, or derive from SVG viewbox
      const padding = Math.max(20, Math.floor(originalSize * 0.1)) // e.g., 10% padding or min 20px

      img.onload = () => {
        // Adjust canvas size to include padding
        canvas.width = originalSize + padding * 2
        canvas.height = originalSize + padding * 2

        if (ctx) {
          // Check if context is not null
          // Fill canvas with white background for the padding
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)

          // Draw the QR code image onto the canvas with padding
          // The img drawn will be originalSize x originalSize, centered in the larger canvas
          ctx.drawImage(img, padding, padding, originalSize, originalSize)
        }

        const pngDataUri = canvas.toDataURL('image/png')

        const link = document.createElement('a')
        link.href = pngDataUri
        link.download = `qr-code-absensi-${student.name.replace(/\s+/g, '-').toLowerCase()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        toast({
          title: 'QR Code Diunduh',
          description: 'QR Code telah berhasil diunduh sebagai PNG.',
        })
      }

      img.onerror = () => {
        toast({
          title: 'Error Konversi',
          description: 'Gagal mengonversi QR Code ke gambar PNG.',
          variant: 'destructive',
        })
      }

      // Set src to SVG data URL to trigger onload
      img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgString)))
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-indigo-600" />
          <p className="text-slate-600 dark:text-slate-300">Memuat data...</p>
          {loadRetries > 0 && (
            <p className="text-xs text-slate-500 dark:text-slate-400">
              Mencoba ulang... {loadRetries}/{MAX_RETRIES}
            </p>
          )}
        </div>
      </div>
    )
  }

  if (profileLoadFailed || !student) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <p className="text-slate-600 dark:text-slate-300">
            {profileLoadFailed
              ? 'Gagal memuat profil. Silakan coba lagi.'
              : 'Data siswa tidak ditemukan'}
          </p>
          <div className="flex space-x-3">
            <button
              onClick={() => router.push('/student')}
              className="rounded-md bg-indigo-600 px-4 py-2 text-white"
            >
              Kembali ke Login
            </button>
            {profileLoadFailed && (
              <button
                onClick={() => {
                  setLoadRetries(0)
                  setLoading(true)
                }}
                className="rounded-md border border-indigo-600 bg-white px-4 py-2 text-indigo-600 dark:bg-transparent dark:text-indigo-400"
              >
                Coba Lagi
              </button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="py-4">
      <div className="flex flex-col items-center justify-center">
        <div className="mb-6 flex w-full flex-col items-center">
          <div className="mb-4 flex items-center space-x-2">
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <Calendar className="h-3.5 w-3.5" />
              <span>{currentDate}</span>
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <Clock className="h-3.5 w-3.5" />
              <span>{currentTime} WITA</span>
            </Badge>
          </div>

          {/* Desktop logout button - hidden on mobile */}
          <div className="absolute right-4 top-4 hidden sm:block">
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="text-slate-600 hover:bg-red-50 hover:text-red-600 dark:text-slate-300 dark:hover:bg-red-900/20 dark:hover:text-red-400"
            >
              Logout
            </Button>
          </div>
        </div>

        {/* QR Code moved to top, outside of tabs */}
        <Card className="relative mb-6 w-full border-4 border-indigo-600 shadow-md dark:border-indigo-600">
          <CardContent className="flex flex-col items-center p-6">
            <div
              className="mb-2 flex items-center justify-center rounded-lg bg-white p-2"
              ref={qrCodeRef}
            >
              <div className="relative flex h-[240px] w-[240px] items-center justify-center bg-white">
                <QRCode
                  value={student.uniqueCode}
                  size={200}
                  style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
                  viewBox={`0 0 256 256`}
                />
                <span className="sr-only">QR Code for {student.uniqueCode}</span>
              </div>
            </div>
            <div className="text-center">
              <p className="font-medium text-slate-800 dark:text-slate-100">QR Code Absensi</p>
              <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Kode: {student.uniqueCode.substring(0, 8)}...
              </p>

              {/* Alert Keamanan QR Code */}
              <Alert variant="destructive" className="mt-4 text-left">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle className="font-semibold">Jaga Kerahasiaan QR Code Anda!</AlertTitle>
                <AlertDescription className="text-xs">
                  QR Code ini adalah kunci akses pribadi Anda untuk absensi. Jangan pernah bagikan
                  atau tunjukkan kepada siapapun. Penyalahgunaan dapat merugikan status kehadiran
                  Anda. Pastikan hanya Anda yang menggunakannya.
                </AlertDescription>
              </Alert>

              {/* Tombol Unduh QR Code */}
              <Button
                variant="outline"
                onClick={handleDownloadQRCode}
                className="mx-auto mt-4 flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Unduh QR Code (PNG)
              </Button>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="status" className="w-full">
          <TabsList className="mb-4 grid w-full grid-cols-2">
            <TabsTrigger value="status">Status Hari Ini</TabsTrigger>
            <TabsTrigger value="schedule">Jadwal</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="mt-0">
            <Card className="shadow-md">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Status Absensi Hari Ini</CardTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={async () => {
                    if (!student) return

                    try {
                      setLoadingAttendance(true)
                      const response = await fetch(
                        `/api/absence/check?uniqueCode=${student.uniqueCode}`
                      )

                      if (!response.ok) {
                        console.error('Failed to fetch attendance status:', response.status)
                        return
                      }

                      const data = await response.json()

                      // Update attendance status based on API response
                      setAttendanceStatus({
                        zuhur: data.zuhr || false,
                        zuhurTime: data.zuhrTime || null,
                        asr: data.asr || false,
                        asrTime: data.asrTime || null,
                        dismissal: data.pulang || false,
                        dismissalTime: data.pulangTime || null,
                      })

                      toast({
                        title: 'Status diperbarui',
                        description: 'Status absensi telah diperbarui',
                      })
                    } catch (error) {
                      console.error('Error refreshing attendance status:', error)
                      toast({
                        title: 'Error',
                        description: 'Gagal memperbarui status absensi',
                        variant: 'destructive',
                      })
                    } finally {
                      setLoadingAttendance(false)
                    }
                  }}
                  disabled={loadingAttendance}
                  className="h-8 w-8"
                  title="Perbarui status"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={`${loadingAttendance ? 'animate-spin' : ''}`}
                  >
                    <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                    <path d="M3 3v5h5" />
                    <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
                    <path d="M16 16h5v5" />
                  </svg>
                </Button>
              </CardHeader>
              <CardContent className="p-6">
                {loadingAttendance && (
                  <div className="flex items-center justify-center py-8">
                    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-indigo-600"></div>
                  </div>
                )}

                {!loadingAttendance && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between rounded-lg bg-slate-100 p-3 dark:bg-slate-800">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-indigo-100 p-2 dark:bg-indigo-900">
                          <span className="font-medium text-indigo-600 dark:text-indigo-400">
                            ZH
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-800 dark:text-slate-100">
                            Shalat Zuhur
                          </h3>
                          {attendanceStatus.zuhur && (
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              {attendanceStatus.zuhurTime}
                            </p>
                          )}
                        </div>
                      </div>
                      {attendanceStatus.zuhur ? (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircle className="h-6 w-6 text-slate-400" />
                      )}
                    </div>

                    <div className="flex items-center justify-between rounded-lg bg-slate-100 p-3 dark:bg-slate-800">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-indigo-100 p-2 dark:bg-indigo-900">
                          <span className="font-medium text-indigo-600 dark:text-indigo-400">
                            AS
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-800 dark:text-slate-100">
                            Shalat Asr
                          </h3>
                          {attendanceStatus.asr && (
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              {attendanceStatus.asrTime}
                            </p>
                          )}
                        </div>
                      </div>
                      {attendanceStatus.asr ? (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircle className="h-6 w-6 text-slate-400" />
                      )}
                    </div>

                    <div className="flex items-center justify-between rounded-lg bg-slate-100 p-3 dark:bg-slate-800">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-indigo-100 p-2 dark:bg-indigo-900">
                          <span className="font-medium text-indigo-600 dark:text-indigo-400">
                            PL
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-800 dark:text-slate-100">Pulang</h3>
                          {attendanceStatus.dismissal && (
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              {attendanceStatus.dismissalTime}
                            </p>
                          )}
                        </div>
                      </div>
                      {attendanceStatus.dismissal ? (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircle className="h-6 w-6 text-slate-400" />
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="mt-0">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg">Jadwal Hari Ini</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-yellow-100 p-2 dark:bg-yellow-900">
                      <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-slate-800 dark:text-slate-100">
                        Shalat Zuhur
                      </h3>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        12:00 - 12:30 WITA
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-yellow-100 p-2 dark:bg-yellow-900">
                      <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-slate-800 dark:text-slate-100">Shalat Asr</h3>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        15:00 - 15:30 WITA
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-yellow-100 p-2 dark:bg-yellow-900">
                      <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-slate-800 dark:text-slate-100">Pulang</h3>
                      <p className="text-sm text-slate-500 dark:text-slate-400">16:30 WITA</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Mobile logout button - shown only on small screens */}
        <div className="mt-8 block sm:hidden">
          <Button
            variant="outline"
            size="sm"
            onClick={handleLogout}
            className="mx-auto flex w-full items-center justify-center text-slate-600 hover:bg-red-50 hover:text-red-600 dark:text-slate-300 dark:hover:bg-red-900/20 dark:hover:text-red-400"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-2 h-4 w-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Logout
          </Button>
        </div>
      </div>
    </div>
  )
}

export default function StudentHome() {
  return (
    <StudentAuthCheck>
      <Suspense fallback={<LoadingFallback />}>
        <StudentHomeContent />
      </Suspense>
    </StudentAuthCheck>
  )
}
