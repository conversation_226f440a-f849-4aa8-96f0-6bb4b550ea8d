/**
 * <PERSON><PERSON><PERSON> to create test users for role-based testing
 * Run with: node scripts/create-test-users.js
 */

const postgres = require('postgres')
const bcrypt = require('bcrypt')
const { v4: uuidv4 } = require('uuid')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function createTestUsers() {
  console.log('🚀 Creating test users for role-based testing...\n')

  try {
    // Get database URL
    const databaseUrl = process.env.DATABASE_URL
    if (!databaseUrl) {
      throw new Error('DATABASE_URL not found in environment variables')
    }

    console.log('📡 Connecting to database...')
    const sql = postgres(databaseUrl)

    // Test users data
    const testUsers = [
      {
        role: 'student',
        name: 'Test Student',
        username: 'teststudent',
        password: 'password123',
        uniqueCode: uuidv4(),
        nis: '12345678'
      },
      {
        role: 'admin',
        name: 'Test Admin',
        username: 'testadmin',
        password: 'password123'
      },
      {
        role: 'super_admin',
        name: 'Test Super Admin',
        username: 'testsuperadmin',
        password: 'password123'
      }
    ]

    console.log('👥 Creating test users...\n')

    for (const userData of testUsers) {
      try {
        // Hash password
        const passwordHash = await bcrypt.hash(userData.password, 10)

        // Prepare user data for database
        const dbUserData = {
          role: userData.role,
          name: userData.name,
          username: userData.username,
          password_hash: passwordHash,
          created_at: new Date()
        }

        // Add student-specific fields
        if (userData.role === 'student') {
          dbUserData.unique_code = userData.uniqueCode
          dbUserData.nis = userData.nis
        }

        // Check if user already exists
        const existingUser = await sql`
          SELECT id FROM users WHERE username = ${userData.username}
        `

        if (existingUser.length > 0) {
          console.log(`⚠️  User ${userData.username} already exists, skipping...`)
          continue
        }

        // Insert user
        const result = await sql`
          INSERT INTO users (
            role, name, username, password_hash, unique_code, nis, created_at
          ) VALUES (
            ${dbUserData.role}, ${dbUserData.name}, ${dbUserData.username},
            ${dbUserData.password_hash}, ${dbUserData.unique_code || null},
            ${dbUserData.nis || null}, ${dbUserData.created_at}
          ) RETURNING id
        `

        const userId = result[0]?.id
        console.log(`✅ Created ${userData.role}: ${userData.name} (ID: ${userId})`)
        console.log(`   Username: ${userData.username}`)
        console.log(`   Password: ${userData.password}`)
        if (userData.uniqueCode) {
          console.log(`   Unique Code: ${userData.uniqueCode}`)
        }
        console.log('')

      } catch (error) {
        console.error(`❌ Failed to create ${userData.role} ${userData.name}:`, error.message)
      }
    }

    // Create test credentials file
    const credentialsContent = `# Test User Credentials for Role-Based Testing
# Generated on: ${new Date().toISOString()}

## Student Account
Username: teststudent
Password: password123
Role: student
Access: /student/home, /student/profile only

## Admin Account  
Username: testadmin
Password: password123
Role: admin
Access: /admin/home (scanner), /admin/reports, /admin/profile only

## Super Admin Account
Username: testsuperadmin  
Password: password123
Role: super_admin
Access: All /admin/* pages

## Testing Instructions

1. Login as each user type at:
   - Student: http://localhost:3000/student
   - Admin: http://localhost:3000/admin
   - Super Admin: http://localhost:3000/admin

2. Verify navigation menus show correct items for each role

3. Test page access restrictions:
   - Students should be redirected if accessing /admin/* pages
   - Admins should be redirected if accessing /admin/users, /admin/admins, etc.
   - Super admins should have access to all pages

4. Test attendance type restrictions in scanner:
   - Admins can only record: Zuhr, Asr, Pulang, Ijin
   - Super admins can record all types

## Security Testing

Try accessing unauthorized URLs directly:
- As student: http://localhost:3000/admin/home (should redirect)
- As admin: http://localhost:3000/admin/users (should redirect)
- As admin: http://localhost:3000/admin/admins (should redirect)
`

    fs.writeFileSync('test-credentials.txt', credentialsContent)
    console.log('📝 Test credentials saved to test-credentials.txt')

    await sql.end()
    console.log('\n🎉 Test users created successfully!')
    console.log('\n📋 Next Steps:')
    console.log('1. Start the development server: npm run dev')
    console.log('2. Test each role by logging in with the credentials above')
    console.log('3. Verify role-based navigation and page access')
    console.log('4. Test attendance type restrictions in scanner')

  } catch (error) {
    console.error('💥 Error creating test users:', error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  createTestUsers()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Script failed:', error)
      process.exit(1)
    })
}

module.exports = { createTestUsers }
