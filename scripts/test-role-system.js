/**
 * Comprehensive test script for role-based access control system
 * Run with: node scripts/test-role-system.js
 */

const fs = require('fs')
const path = require('path')

function testFileStructure() {
  console.log('📁 Testing Role Configuration File Structure...\n')

  const requiredFiles = [
    'lib/config/role-permissions.ts',
    'lib/utils/attendance-validation.ts',
    'components/admin-bottom-nav.tsx',
    'components/student-bottom-nav.tsx',
    'components/layouts/admin-layout.tsx',
    'middleware.ts',
    'app/admin/home/<USER>'
  ]

  let allFilesExist = true

  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file} - MISSING`)
      allFilesExist = false
    }
  }

  return allFilesExist
}

function testRoleConfiguration() {
  console.log('\n🔧 Testing Role Configuration...\n')

  try {
    // Read the role permissions file
    const rolePermissionsPath = 'lib/config/role-permissions.ts'
    const content = fs.readFileSync(rolePermissionsPath, 'utf8')

    // Check for required exports
    const requiredExports = [
      'export type UserRole',
      'export interface RoleConfig',
      'export const ROLE_CONFIG',
      'export function canAccessPage',
      'export function getNavigationItems',
      'export function canHandleAttendanceType',
      'export function getDefaultRedirect'
    ]

    let allExportsFound = true
    for (const exportItem of requiredExports) {
      if (content.includes(exportItem)) {
        console.log(`✅ ${exportItem}`)
      } else {
        console.log(`❌ ${exportItem} - MISSING`)
        allExportsFound = false
      }
    }

    // Check role configurations
    const roles = ['student', 'admin', 'super_admin']
    for (const role of roles) {
      if (content.includes(`${role}:`)) {
        console.log(`✅ Role configuration for: ${role}`)
      } else {
        console.log(`❌ Role configuration for: ${role} - MISSING`)
        allExportsFound = false
      }
    }

    return allExportsFound

  } catch (error) {
    console.error('❌ Error reading role configuration:', error.message)
    return false
  }
}

function testAttendanceValidation() {
  console.log('\n🎯 Testing Attendance Validation...\n')

  try {
    const validationPath = 'lib/utils/attendance-validation.ts'
    const content = fs.readFileSync(validationPath, 'utf8')

    const requiredFunctions = [
      'export function validateAttendanceTypeAccess',
      'export function getAllowedAttendanceTypes',
      'export function getAttendanceTypeLabel',
      'export function isValidAttendanceType'
    ]

    let allFunctionsFound = true
    for (const func of requiredFunctions) {
      if (content.includes(func)) {
        console.log(`✅ ${func}`)
      } else {
        console.log(`❌ ${func} - MISSING`)
        allFunctionsFound = false
      }
    }

    return allFunctionsFound

  } catch (error) {
    console.error('❌ Error reading attendance validation:', error.message)
    return false
  }
}

function testNavigationComponents() {
  console.log('\n🧭 Testing Navigation Components...\n')

  const components = [
    'components/admin-bottom-nav.tsx',
    'components/student-bottom-nav.tsx'
  ]

  let allComponentsValid = true

  for (const component of components) {
    try {
      const content = fs.readFileSync(component, 'utf8')

      if (content.includes('getNavigationItems')) {
        console.log(`✅ ${component} - Uses role-based navigation`)
      } else {
        console.log(`❌ ${component} - Missing role-based navigation`)
        allComponentsValid = false
      }

    } catch (error) {
      console.log(`❌ ${component} - Error reading file: ${error.message}`)
      allComponentsValid = false
    }
  }

  return allComponentsValid
}

function testMiddleware() {
  console.log('\n🛡️ Testing Middleware Security...\n')

  try {
    const middlewarePath = 'middleware.ts'
    const content = fs.readFileSync(middlewarePath, 'utf8')

    const securityFeatures = [
      'canAccessPage',
      'getDefaultRedirect',
      'UserRole',
      'Role-based page access control'
    ]

    let allFeaturesFound = true
    for (const feature of securityFeatures) {
      if (content.includes(feature)) {
        console.log(`✅ Middleware includes: ${feature}`)
      } else {
        console.log(`❌ Middleware missing: ${feature}`)
        allFeaturesFound = false
      }
    }

    return allFeaturesFound

  } catch (error) {
    console.error('❌ Error reading middleware:', error.message)
    return false
  }
}

function testAPIRoutes() {
  console.log('\n🔌 Testing API Route Security...\n')

  const apiRoutes = [
    'app/api/absence/record/route.ts'
  ]

  let allRoutesSecure = true

  for (const route of apiRoutes) {
    try {
      const content = fs.readFileSync(route, 'utf8')

      if (content.includes('validateAttendanceTypeAccess')) {
        console.log(`✅ ${route} - Has attendance type validation`)
      } else {
        console.log(`❌ ${route} - Missing attendance type validation`)
        allRoutesSecure = false
      }

    } catch (error) {
      console.log(`❌ ${route} - Error reading file: ${error.message}`)
      allRoutesSecure = false
    }
  }

  return allRoutesSecure
}

function generateTestReport() {
  console.log('\n📊 Generating Test Report...\n')

  const testResults = {
    fileStructure: testFileStructure(),
    roleConfiguration: testRoleConfiguration(),
    attendanceValidation: testAttendanceValidation(),
    navigationComponents: testNavigationComponents(),
    middleware: testMiddleware(),
    apiRoutes: testAPIRoutes()
  }

  const allTestsPassed = Object.values(testResults).every(result => result === true)

  console.log('\n📋 TEST SUMMARY')
  console.log('================')

  for (const [test, passed] of Object.entries(testResults)) {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    console.log(`${status} - ${testName}`)
  }

  console.log('\n' + '='.repeat(50))

  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED! Role-based system is ready!')
    console.log('\n📋 Next Steps:')
    console.log('1. Run: node scripts/create-test-users.js')
    console.log('2. Start server: npm run dev')
    console.log('3. Test each role manually with the created users')
    console.log('4. Verify navigation and page access restrictions')
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.')
    console.log('\n🔧 Recommended Actions:')
    console.log('1. Check missing files and implementations')
    console.log('2. Ensure all role-based functions are properly exported')
    console.log('3. Verify security validations are in place')
  }

  return allTestsPassed
}

// Run all tests
if (require.main === module) {
  console.log('🧪 ROLE-BASED ACCESS CONTROL SYSTEM TEST')
  console.log('='.repeat(50))

  const success = generateTestReport()
  process.exit(success ? 0 : 1)
}

module.exports = {
  testFileStructure,
  testRoleConfiguration,
  testAttendanceValidation,
  testNavigationComponents,
  testMiddleware,
  testAPIRoutes,
  generateTestReport
}
