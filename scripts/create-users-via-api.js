/**
 * Create test users via API calls
 * This script creates users through the application's API endpoints
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

// First, let's create a super admin directly via the admin creation API
async function createSuperAdmin() {
  console.log('🚀 Creating Super Admin...')

  try {
    const response = await fetch(`${BASE_URL}/api/admins`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role: 'super_admin',
        name: 'Test Super Admin',
        username: 'testsuperadmin',
        password: 'password123'
      })
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Super Admin created successfully:', data)
      return data
    } else {
      console.log('⚠️ Super Admin creation response:', data)
      // If user already exists, that's fine
      if (data.message && data.message.includes('already exists')) {
        console.log('✅ Super Admin already exists, continuing...')
        return { username: 'testsuperadmin' }
      }
      throw new Error(data.message || 'Failed to create super admin')
    }
  } catch (error) {
    console.error('❌ Error creating super admin:', error.message)
    throw error
  }
}

// Login as super admin to get auth token
async function loginSuperAdmin() {
  console.log('🔐 Logging in as Super Admin...')

  try {
    const response = await fetch(`${BASE_URL}/api/auth/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testsuperadmin',
        password: 'password123'
      })
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Super Admin login successful')

      // Extract auth token from Set-Cookie header
      const setCookieHeader = response.headers.get('set-cookie')
      if (setCookieHeader) {
        const tokenMatch = setCookieHeader.match(/admin_auth_token=([^;]+)/)
        if (tokenMatch) {
          return tokenMatch[1]
        }
      }

      throw new Error('Auth token not found in response')
    } else {
      console.error('❌ Super Admin login failed:', data)
      throw new Error(data.message || 'Login failed')
    }
  } catch (error) {
    console.error('❌ Error logging in:', error.message)
    throw error
  }
}

// Create other users using the authenticated super admin
async function createUsersWithAuth(authToken) {
  console.log('👥 Creating other test users...')

  const users = [
    {
      role: 'admin',
      name: 'Test Admin',
      username: 'testadmin',
      password: 'password123'
    },
    {
      role: 'student',
      name: 'Test Student',
      username: 'teststudent',
      password: 'password123'
    }
  ]

  for (const user of users) {
    try {
      console.log(`Creating ${user.role}: ${user.username}...`)

      const response = await fetch(`${BASE_URL}/api/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `admin_auth_token=${authToken}`
        },
        body: JSON.stringify(user)
      })

      const data = await response.json()

      if (response.ok) {
        console.log(`✅ ${user.role} created successfully:`, data)
      } else {
        console.log(`⚠️ ${user.role} creation response:`, data)
        if (data.message && data.message.includes('already exists')) {
          console.log(`✅ ${user.role} already exists, continuing...`)
        } else {
          console.error(`❌ Failed to create ${user.role}:`, data.message)
        }
      }
    } catch (error) {
      console.error(`❌ Error creating ${user.role}:`, error.message)
    }
  }
}

// Test login for each user
async function testUserLogins() {
  console.log('\n🧪 Testing user logins...')

  const testUsers = [
    { username: 'testsuperadmin', password: 'password123', type: 'admin', role: 'super_admin' },
    { username: 'testadmin', password: 'password123', type: 'admin', role: 'admin' },
    { username: 'teststudent', password: 'password123', type: 'student', role: 'student' }
  ]

  for (const user of testUsers) {
    try {
      console.log(`\nTesting login for ${user.username} (${user.role})...`)

      const endpoint = user.type === 'admin' ? '/api/auth/admin/login' : '/api/auth/student/login'

      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: user.username,
          password: user.password
        })
      })

      const data = await response.json()

      if (response.ok) {
        console.log(`✅ ${user.username} login successful`)
        console.log(`   Role: ${data.user?.role || data.admin?.role || 'unknown'}`)
        console.log(`   Name: ${data.user?.name || data.admin?.name || 'unknown'}`)
      } else {
        console.error(`❌ ${user.username} login failed:`, data.message)
      }
    } catch (error) {
      console.error(`❌ Error testing login for ${user.username}:`, error.message)
    }
  }
}

// Main function
async function main() {
  console.log('🎯 Creating and Testing Users via API\n')
  console.log('=====================================\n')

  try {
    // Step 1: Create super admin
    await createSuperAdmin()

    // Step 2: Login as super admin to get auth token
    const authToken = await loginSuperAdmin()

    // Step 3: Create other users
    await createUsersWithAuth(authToken)

    // Step 4: Test all logins
    await testUserLogins()

    console.log('\n🎉 User creation and testing completed!')
    console.log('\n📋 Test Credentials:')
    console.log('====================')
    console.log('Super Admin: testsuperadmin / password123')
    console.log('Admin: testadmin / password123')
    console.log('Student: teststudent / password123')
    console.log('\n🌐 Test URLs:')
    console.log('=============')
    console.log('Admin Login: http://localhost:3000/admin')
    console.log('Student Login: http://localhost:3000/student')

  } catch (error) {
    console.error('\n💥 Script failed:', error.message)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { main }
