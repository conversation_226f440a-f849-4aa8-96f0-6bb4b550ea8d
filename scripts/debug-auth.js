/**
 * Debug authentication and role-based access
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function debugAuth() {
  console.log('🔍 Debugging Authentication and Role Access...\n')

  // Test 1: Login as student and get token
  console.log('1. Testing Student Login...')
  try {
    const studentLogin = await fetch(`${BASE_URL}/api/auth/student/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'teststudent', password: 'password123' })
    })

    console.log(`Student login status: ${studentLogin.status}`)
    
    if (studentLogin.ok) {
      const studentData = await studentLogin.json()
      console.log('Student login data:', studentData)
      
      const setCookie = studentLogin.headers.get('set-cookie')
      console.log('Student set-cookie header:', setCookie)
      
      // Extract student token
      const studentTokenMatch = setCookie?.match(/student_auth_token=([^;]+)/)
      const studentToken = studentTokenMatch ? studentTokenMatch[1] : null
      console.log('Student token:', studentToken ? 'Found' : 'Not found')
      
      // Test 2: Try to access admin endpoint with student token
      console.log('\n2. Testing Student Token on Admin Endpoint...')
      const adminEndpointTest = await fetch(`${BASE_URL}/api/users`, {
        headers: {
          'Cookie': `student_auth_token=${studentToken}`
        }
      })
      
      console.log(`Admin endpoint with student token status: ${adminEndpointTest.status}`)
      if (!adminEndpointTest.ok) {
        const errorData = await adminEndpointTest.json()
        console.log('Error response:', errorData)
      }
    }
  } catch (error) {
    console.error('Student test error:', error.message)
  }

  console.log('\n' + '='.repeat(50))

  // Test 3: Login as admin and get token
  console.log('\n3. Testing Admin Login...')
  try {
    const adminLogin = await fetch(`${BASE_URL}/api/auth/admin/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'testadmin', password: 'password123' })
    })

    console.log(`Admin login status: ${adminLogin.status}`)
    
    if (adminLogin.ok) {
      const adminData = await adminLogin.json()
      console.log('Admin login data:', adminData)
      
      const setCookie = adminLogin.headers.get('set-cookie')
      console.log('Admin set-cookie header:', setCookie)
      
      // Extract admin token
      const adminTokenMatch = setCookie?.match(/admin_auth_token=([^;]+)/)
      const adminToken = adminTokenMatch ? adminTokenMatch[1] : null
      console.log('Admin token:', adminToken ? 'Found' : 'Not found')
      
      // Test 4: Try to access admin endpoint with admin token
      console.log('\n4. Testing Admin Token on Admin Endpoint...')
      const adminEndpointTest = await fetch(`${BASE_URL}/api/users`, {
        headers: {
          'Cookie': `admin_auth_token=${adminToken}`
        }
      })
      
      console.log(`Admin endpoint with admin token status: ${adminEndpointTest.status}`)
      if (adminEndpointTest.ok) {
        console.log('✅ Admin can access admin endpoint')
      } else {
        const errorData = await adminEndpointTest.json()
        console.log('Error response:', errorData)
      }
    }
  } catch (error) {
    console.error('Admin test error:', error.message)
  }

  console.log('\n' + '='.repeat(50))

  // Test 5: Try to access admin endpoint without any token
  console.log('\n5. Testing Admin Endpoint Without Token...')
  try {
    const noTokenTest = await fetch(`${BASE_URL}/api/users`)
    console.log(`Admin endpoint without token status: ${noTokenTest.status}`)
    
    if (!noTokenTest.ok) {
      const errorData = await noTokenTest.json()
      console.log('Error response:', errorData)
    }
  } catch (error) {
    console.error('No token test error:', error.message)
  }

  console.log('\n' + '='.repeat(50))

  // Test 6: Try to access admin endpoint with wrong cookie name
  console.log('\n6. Testing Admin Endpoint With Wrong Cookie Name...')
  try {
    // Login as student first
    const studentLogin = await fetch(`${BASE_URL}/api/auth/student/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'teststudent', password: 'password123' })
    })

    if (studentLogin.ok) {
      const setCookie = studentLogin.headers.get('set-cookie')
      const studentTokenMatch = setCookie?.match(/student_auth_token=([^;]+)/)
      const studentToken = studentTokenMatch ? studentTokenMatch[1] : null

      if (studentToken) {
        // Try to use student token as admin token (wrong cookie name)
        const wrongCookieTest = await fetch(`${BASE_URL}/api/users`, {
          headers: {
            'Cookie': `admin_auth_token=${studentToken}`
          }
        })
        
        console.log(`Admin endpoint with student token as admin cookie status: ${wrongCookieTest.status}`)
        
        if (!wrongCookieTest.ok) {
          const errorData = await wrongCookieTest.json()
          console.log('Error response:', errorData)
        } else {
          console.log('⚠️ This should not succeed!')
        }
      }
    }
  } catch (error) {
    console.error('Wrong cookie test error:', error.message)
  }
}

// Run the debug
debugAuth().catch(console.error)
