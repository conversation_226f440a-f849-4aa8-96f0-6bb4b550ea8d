/**
 * <PERSON><PERSON><PERSON> to create a super admin user directly via API
 * This creates the initial super admin that can then create other test users
 * Run with: node scripts/create-super-admin.js
 */

const bcrypt = require('bcrypt')
const { v4: uuidv4 } = require('uuid')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function createSuperAdminDirectly() {
  console.log('🚀 Creating initial super admin user...\n')

  try {
    // We'll create this user by directly inserting into database using a simple approach
    // Since we can't connect to DB directly, let's create a manual SQL command
    
    const superAdminData = {
      username: 'testsuperadmin',
      name: 'Test Super Admin',
      password: 'password123',
      role: 'super_admin'
    }

    // Hash the password
    const passwordHash = await bcrypt.hash(superAdminData.password, 10)

    console.log('📋 Super Admin User Data:')
    console.log('========================')
    console.log(`Username: ${superAdminData.username}`)
    console.log(`Password: ${superAdminData.password}`)
    console.log(`Name: ${superAdminData.name}`)
    console.log(`Role: ${superAdminData.role}`)
    console.log(`Password Hash: ${passwordHash}`)
    console.log('')

    // Generate SQL command
    const sqlCommand = `
INSERT INTO users (role, name, username, password_hash, created_at) 
VALUES ('super_admin', '${superAdminData.name}', '${superAdminData.username}', '${passwordHash}', NOW())
ON CONFLICT (username) DO NOTHING;
`

    console.log('📝 SQL Command to run in your database:')
    console.log('========================================')
    console.log(sqlCommand)
    console.log('')

    console.log('🔧 Instructions:')
    console.log('1. Connect to your PostgreSQL database')
    console.log('2. Run the SQL command above')
    console.log('3. Or use the API approach below if you have an existing super admin')
    console.log('')

    // Also provide curl commands for API creation
    console.log('🌐 Alternative: Create via API (if you have existing super admin):')
    console.log('================================================================')
    
    const apiCommands = [
      {
        role: 'student',
        name: 'Test Student',
        username: 'teststudent',
        password: 'password123'
      },
      {
        role: 'admin', 
        name: 'Test Admin',
        username: 'testadmin',
        password: 'password123'
      }
    ]

    apiCommands.forEach(user => {
      const curlCommand = `curl -X POST http://localhost:3000/api/users \\
  -H "Content-Type: application/json" \\
  -H "Cookie: admin_auth_token=YOUR_ADMIN_TOKEN" \\
  -d '{
    "role": "${user.role}",
    "name": "${user.name}",
    "username": "${user.username}",
    "password": "${user.password}"
  }'`
      
      console.log(`\n# Create ${user.role}:`)
      console.log(curlCommand)
    })

    console.log('\n📋 Test Credentials Summary:')
    console.log('============================')
    console.log('Super Admin: testsuperadmin / password123')
    console.log('Admin: testadmin / password123') 
    console.log('Student: teststudent / password123')
    console.log('')

    console.log('🧪 Testing Instructions:')
    console.log('========================')
    console.log('1. Login as super admin at: http://localhost:3000/admin')
    console.log('2. Navigate to different admin pages to test access')
    console.log('3. Check navigation menu shows all items for super admin')
    console.log('4. Test scanner with all attendance types')
    console.log('5. Login as regular admin and verify restricted access')
    console.log('6. Login as student and verify student-only access')

  } catch (error) {
    console.error('💥 Error:', error)
  }
}

// Run the script
if (require.main === module) {
  createSuperAdminDirectly()
}

module.exports = { createSuperAdminDirectly }
