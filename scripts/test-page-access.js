/**
 * Test page access function
 */

// Import the role permissions
const { canAccessPage } = require('../lib/config/role-permissions.ts')

function testPageAccess() {
  console.log('🧪 Testing Page Access Function\n')

  const testCases = [
    { role: 'super_admin', page: '/admin/home' },
    { role: 'super_admin', page: '/admin/sessions' },
    { role: 'super_admin', page: '/admin/users' },
    { role: 'super_admin', page: '/api/users' },
    { role: 'super_admin', page: '/api/admins' },
    { role: 'super_admin', page: '/api/admin/sessions' },
    { role: 'admin', page: '/admin/home' },
    { role: 'admin', page: '/admin/sessions' },
    { role: 'admin', page: '/api/users' },
    { role: 'student', page: '/student/home' },
    { role: 'student', page: '/admin/home' },
    { role: 'student', page: '/api/users' }
  ]

  for (const testCase of testCases) {
    const result = canAccessPage(testCase.role, testCase.page)
    const status = result ? '✅ ALLOWED' : '❌ DENIED'
    console.log(`${status} - ${testCase.role} accessing ${testCase.page}`)
  }
}

testPageAccess()
