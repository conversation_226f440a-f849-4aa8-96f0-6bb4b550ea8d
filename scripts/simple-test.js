/**
 * Simple test to verify authentication
 */

const { default: fetch } = require('node-fetch')

async function simpleTest() {
  console.log('🧪 Simple Authentication Test\n')

  // Login as student
  console.log('1. <PERSON>gin as student...')
  const studentLogin = await fetch('http://localhost:3001/api/auth/student/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'teststudent', password: 'password123' })
  })

  console.log('Student login status:', studentLogin.status)

  if (studentLogin.ok) {
    const setCookie = studentLogin.headers.get('set-cookie')
    const studentTokenMatch = setCookie?.match(/student_auth_token=([^;]+)/)
    const studentToken = studentTokenMatch ? studentTokenMatch[1] : null

    if (studentToken) {
      console.log('Student token found:', studentToken.substring(0, 20) + '...')

      // Test admin endpoint with student token
      console.log('\n2. Testing admin endpoint with student token...')
      const adminTest = await fetch('http://localhost:3001/api/users', {
        headers: {
          'Cookie': `student_auth_token=${studentToken}`
        }
      })

      console.log('Admin endpoint status:', adminTest.status)
      console.log('Admin endpoint ok:', adminTest.ok)

      if (!adminTest.ok) {
        const errorData = await adminTest.json()
        console.log('Error response:', errorData)
      } else {
        console.log('⚠️ This should not have succeeded!')
        const data = await adminTest.json()
        console.log('Response data:', data)
      }
    }
  }
}

simpleTest().catch(console.error)
