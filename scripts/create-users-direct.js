/**
 * Create test users directly in the database
 * This script bypasses API authentication and creates users directly
 */

const { Client } = require('pg')
const bcrypt = require('bcrypt')
const { v4: uuidv4 } = require('uuid')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function createUsersDirectly() {
  console.log('🚀 Creating test users directly in database...\n')

  // Parse DATABASE_URL
  const databaseUrl = process.env.DATABASE_URL
  if (!databaseUrl) {
    throw new Error('DATABASE_URL not found in environment variables')
  }

  console.log('📡 Connecting to database...')
  const client = new Client({
    connectionString: databaseUrl
  })

  try {
    await client.connect()
    console.log('✅ Database connected successfully')

    // Test users to create
    const testUsers = [
      {
        role: 'super_admin',
        name: 'Test Super Admin',
        username: 'testsuperadmin',
        password: 'password123'
      },
      {
        role: 'admin',
        name: '<PERSON> Admin',
        username: 'testadmin',
        password: 'password123'
      },
      {
        role: 'student',
        name: 'Test Student',
        username: 'teststudent',
        password: 'password123',
        uniqueCode: uuidv4(),
        nis: '12345678'
      }
    ]

    console.log('\n👥 Creating test users...\n')

    for (const userData of testUsers) {
      try {
        console.log(`Creating ${userData.role}: ${userData.username}...`)

        // Check if user already exists
        const existingUser = await client.query(
          'SELECT id FROM users WHERE username = $1',
          [userData.username]
        )

        if (existingUser.rows.length > 0) {
          console.log(`⚠️  User ${userData.username} already exists, skipping...`)
          continue
        }

        // Hash password
        const passwordHash = await bcrypt.hash(userData.password, 10)

        // Insert user
        const insertQuery = `
          INSERT INTO users (
            role, name, username, password_hash, unique_code, nis, created_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, NOW()
          ) RETURNING id, role, name, username
        `

        const values = [
          userData.role,
          userData.name,
          userData.username,
          passwordHash,
          userData.uniqueCode || null,
          userData.nis || null
        ]

        const result = await client.query(insertQuery, values)
        const createdUser = result.rows[0]

        console.log(`✅ Created ${userData.role}: ${createdUser.name} (ID: ${createdUser.id})`)
        console.log(`   Username: ${createdUser.username}`)
        console.log(`   Password: ${userData.password}`)
        if (userData.uniqueCode) {
          console.log(`   Unique Code: ${userData.uniqueCode}`)
        }
        console.log('')

      } catch (error) {
        console.error(`❌ Failed to create ${userData.role} ${userData.name}:`, error.message)
      }
    }

    // Verify users were created
    console.log('🔍 Verifying created users...\n')
    const allUsers = await client.query(
      'SELECT id, role, name, username, unique_code FROM users ORDER BY role, name'
    )

    console.log('📋 Users in database:')
    console.log('=====================')
    allUsers.rows.forEach(user => {
      console.log(`${user.role.toUpperCase()}: ${user.name} (${user.username})`)
      if (user.unique_code) {
        console.log(`  Unique Code: ${user.unique_code}`)
      }
    })

    console.log('\n🎉 User creation completed successfully!')
    console.log('\n📋 Test Credentials:')
    console.log('====================')
    console.log('Super Admin: testsuperadmin / password123')
    console.log('Admin: testadmin / password123')
    console.log('Student: teststudent / password123')

    console.log('\n🌐 Test URLs:')
    console.log('=============')
    console.log('Admin Login: http://localhost:3000/admin')
    console.log('Student Login: http://localhost:3000/student')

    console.log('\n🧪 Next Steps:')
    console.log('==============')
    console.log('1. Test login with each user type')
    console.log('2. Verify role-based navigation menus')
    console.log('3. Test page access restrictions')
    console.log('4. Test attendance type restrictions in scanner')

  } catch (error) {
    console.error('💥 Database error:', error)
    throw error
  } finally {
    await client.end()
    console.log('\n📡 Database connection closed')
  }
}

// Test login function
async function testUserLogins() {
  console.log('\n🧪 Testing user logins via API...')
  
  const { default: fetch } = require('node-fetch')
  const BASE_URL = 'http://localhost:3000'

  const testUsers = [
    { username: 'testsuperadmin', password: 'password123', type: 'admin', role: 'super_admin' },
    { username: 'testadmin', password: 'password123', type: 'admin', role: 'admin' },
    { username: 'teststudent', password: 'password123', type: 'student', role: 'student' }
  ]

  for (const user of testUsers) {
    try {
      console.log(`\nTesting login for ${user.username} (${user.role})...`)
      
      const endpoint = user.type === 'admin' ? '/api/auth/admin/login' : '/api/auth/student/login'
      
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: user.username,
          password: user.password
        })
      })

      const data = await response.json()
      
      if (response.ok) {
        console.log(`✅ ${user.username} login successful`)
        console.log(`   Role: ${data.user?.role || data.admin?.role || 'unknown'}`)
        console.log(`   Name: ${data.user?.name || data.admin?.name || 'unknown'}`)
      } else {
        console.error(`❌ ${user.username} login failed:`, data.message)
      }
    } catch (error) {
      console.error(`❌ Error testing login for ${user.username}:`, error.message)
    }
  }
}

// Main function
async function main() {
  console.log('🎯 CREATING TEST USERS FOR ROLE-BASED TESTING')
  console.log('=' .repeat(50))
  
  try {
    // Create users in database
    await createUsersDirectly()
    
    // Test logins
    await testUserLogins()
    
    console.log('\n🎉 ALL DONE! Users created and tested successfully!')
    console.log('\n📋 Ready for manual testing:')
    console.log('- Open http://localhost:3000/admin and login as testsuperadmin')
    console.log('- Test navigation and page access for each role')
    console.log('- Verify attendance type restrictions in scanner')
    
  } catch (error) {
    console.error('\n💥 Script failed:', error.message)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { main, createUsersDirectly, testUserLogins }
