/**
 * Final comprehensive test of all roles
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function loginUser(username, password, userType = 'admin') {
  const endpoint = userType === 'admin' ? '/api/auth/admin/login' : '/api/auth/student/login'
  
  const response = await fetch(`${BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  })

  if (!response.ok) {
    throw new Error(`Lo<PERSON> failed for ${username}`)
  }

  const setCookieHeader = response.headers.get('set-cookie')
  if (setCookieHeader) {
    const tokenMatch = setCookieHeader.match(/(admin_auth_token|student_auth_token)=([^;]+)/)
    if (tokenMatch) {
      return { token: tokenMatch[2], tokenName: tokenMatch[1] }
    }
  }
  
  throw new Error('Auth token not found')
}

async function testApiAccess(endpoint, authToken, tokenName, expectedStatus = 200) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Cookie': `${tokenName}=${authToken}`
      }
    })

    return {
      status: response.status,
      success: response.status === expectedStatus,
      endpoint
    }
  } catch (error) {
    return {
      status: 'ERROR',
      success: false,
      endpoint,
      error: error.message
    }
  }
}

async function testAllRoles() {
  console.log('🎯 FINAL COMPREHENSIVE ROLE TESTING')
  console.log('=' .repeat(50))
  
  // Test users
  const testUsers = {
    superAdmin: { username: 'testsuperadmin', password: 'password123', role: 'super_admin' },
    admin: { username: 'testadmin', password: 'password123', role: 'admin' },
    student: { username: 'teststudent', password: 'password123', role: 'student' }
  }

  const results = {
    superAdmin: { passed: 0, failed: 0, tests: [] },
    admin: { passed: 0, failed: 0, tests: [] },
    student: { passed: 0, failed: 0, tests: [] }
  }

  // Test Super Admin (should have full access)
  console.log('\n1. 🔴 TESTING SUPER ADMIN (Full Access)')
  console.log('=' .repeat(40))
  try {
    const { token, tokenName } = await loginUser(testUsers.superAdmin.username, testUsers.superAdmin.password)
    console.log('✅ Super Admin login successful')
    
    const superAdminEndpoints = [
      { endpoint: '/api/users', expectedStatus: 200 },
      { endpoint: '/api/admins', expectedStatus: 200 },
      { endpoint: '/api/classes', expectedStatus: 200 },
      { endpoint: '/api/admin/sessions', expectedStatus: 200 },
      { endpoint: '/api/admin/sessions/stats', expectedStatus: 200 }
    ]

    for (const { endpoint, expectedStatus } of superAdminEndpoints) {
      const result = await testApiAccess(endpoint, token, tokenName, expectedStatus)
      results.superAdmin.tests.push(result)
      
      if (result.success) {
        results.superAdmin.passed++
        console.log(`  ✅ ${endpoint} - Status: ${result.status}`)
      } else {
        results.superAdmin.failed++
        console.log(`  ❌ ${endpoint} - Status: ${result.status} (expected: ${expectedStatus})`)
      }
    }
  } catch (error) {
    console.error(`❌ Super Admin test failed: ${error.message}`)
    results.superAdmin.failed++
  }

  // Test Admin (should have limited access)
  console.log('\n2. 🔵 TESTING ADMIN (Limited Access)')
  console.log('=' .repeat(40))
  try {
    const { token, tokenName } = await loginUser(testUsers.admin.username, testUsers.admin.password)
    console.log('✅ Admin login successful')
    
    const adminEndpoints = [
      { endpoint: '/api/users', expectedStatus: 200 },          // Should have access
      { endpoint: '/api/admins', expectedStatus: 200 },         // Should have access
      { endpoint: '/api/classes', expectedStatus: 200 },        // Should have access
      { endpoint: '/api/admin/sessions', expectedStatus: 403 }, // Should NOT have access (super admin only)
      { endpoint: '/api/admin/sessions/stats', expectedStatus: 403 } // Should NOT have access (super admin only)
    ]

    for (const { endpoint, expectedStatus } of adminEndpoints) {
      const result = await testApiAccess(endpoint, token, tokenName, expectedStatus)
      results.admin.tests.push(result)
      
      if (result.success) {
        results.admin.passed++
        console.log(`  ✅ ${endpoint} - Status: ${result.status} (expected: ${expectedStatus})`)
      } else {
        results.admin.failed++
        console.log(`  ❌ ${endpoint} - Status: ${result.status} (expected: ${expectedStatus})`)
      }
    }
  } catch (error) {
    console.error(`❌ Admin test failed: ${error.message}`)
    results.admin.failed++
  }

  // Test Student (should have no admin access)
  console.log('\n3. 🟢 TESTING STUDENT (No Admin Access)')
  console.log('=' .repeat(40))
  try {
    const { token, tokenName } = await loginUser(testUsers.student.username, testUsers.student.password, 'student')
    console.log('✅ Student login successful')
    
    // Test admin endpoints with student token - should all fail
    const studentEndpoints = [
      { endpoint: '/api/users', expectedStatus: 401 },
      { endpoint: '/api/admins', expectedStatus: 401 },
      { endpoint: '/api/classes', expectedStatus: 401 },
      { endpoint: '/api/admin/sessions', expectedStatus: 401 },
      { endpoint: '/api/admin/sessions/stats', expectedStatus: 401 }
    ]

    for (const { endpoint, expectedStatus } of studentEndpoints) {
      // Try to access admin endpoint with student token (wrong cookie name)
      const result = await testApiAccess(endpoint, token, 'admin_auth_token', expectedStatus)
      results.student.tests.push(result)
      
      if (result.success) {
        results.student.passed++
        console.log(`  ✅ ${endpoint} - Status: ${result.status} (correctly blocked)`)
      } else {
        results.student.failed++
        console.log(`  ❌ ${endpoint} - Status: ${result.status} (expected: ${expectedStatus})`)
      }
    }
  } catch (error) {
    console.error(`❌ Student test failed: ${error.message}`)
    results.student.failed++
  }

  // Generate final report
  console.log('\n🏆 FINAL TEST RESULTS')
  console.log('=' .repeat(50))
  
  const totalPassed = results.superAdmin.passed + results.admin.passed + results.student.passed
  const totalFailed = results.superAdmin.failed + results.admin.failed + results.student.failed
  const totalTests = totalPassed + totalFailed
  
  console.log(`Super Admin: ${results.superAdmin.passed}/${results.superAdmin.tests.length} passed`)
  console.log(`Admin: ${results.admin.passed}/${results.admin.tests.length} passed`)
  console.log(`Student: ${results.student.passed}/${results.student.tests.length} passed`)
  console.log('')
  console.log(`OVERALL: ${totalPassed}/${totalTests} tests passed`)
  
  const allPassed = totalFailed === 0
  console.log(`\n🎯 RESULT: ${allPassed ? '✅ ALL TESTS PASSED!' : '❌ SOME TESTS FAILED'}`)
  
  if (allPassed) {
    console.log('\n🎉 ROLE-BASED ACCESS CONTROL IS WORKING PERFECTLY!')
    console.log('✅ Super admin has full access')
    console.log('✅ Admin has limited access')
    console.log('✅ Student has no admin access')
    console.log('✅ All security restrictions are working')
    console.log('\n🚀 Ready for production deployment!')
  } else {
    console.log('\n⚠️ Some tests failed. Please review the results above.')
  }
  
  return allPassed
}

testAllRoles().catch(console.error)
