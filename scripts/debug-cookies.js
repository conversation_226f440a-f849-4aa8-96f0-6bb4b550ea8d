/**
 * Debug cookie handling in authentication
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3001'

async function debugCookies() {
  console.log('🍪 Debugging Cookie Handling...\n')

  // Step 1: <PERSON><PERSON> as student and get token
  console.log('1. <PERSON><PERSON> as student...')
  const studentLogin = await fetch(`${BASE_URL}/api/auth/student/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'teststudent', password: 'password123' })
  })

  const setCookieHeader = studentLogin.headers.get('set-cookie')
  console.log('Student set-cookie header:', setCookieHeader)

  // Extract student token
  const studentTokenMatch = setCookieHeader?.match(/student_auth_token=([^;]+)/)
  const studentToken = studentTokenMatch ? studentTokenMatch[1] : null
  console.log('Student token extracted:', studentToken ? 'YES' : 'NO')

  if (!studentToken) {
    console.error('❌ Could not extract student token')
    return
  }

  // Step 2: Test different cookie combinations
  console.log('\n2. Testing different cookie combinations...\n')

  // Test A: Send only student_auth_token to admin endpoint
  console.log('Test A: Only student_auth_token cookie to admin endpoint')
  const testA = await fetch(`${BASE_URL}/api/users`, {
    headers: {
      'Cookie': `student_auth_token=${studentToken}`
    }
  })
  console.log(`Result: ${testA.status}`)
  if (!testA.ok) {
    const errorA = await testA.json()
    console.log('Error:', errorA)
  } else {
    console.log('⚠️ This should have failed!')
  }

  // Test B: Send student token as admin_auth_token
  console.log('\nTest B: Student token as admin_auth_token cookie')
  const testB = await fetch(`${BASE_URL}/api/users`, {
    headers: {
      'Cookie': `admin_auth_token=${studentToken}`
    }
  })
  console.log(`Result: ${testB.status}`)
  if (!testB.ok) {
    const errorB = await testB.json()
    console.log('Error:', errorB)
  }

  // Test C: Send both cookies
  console.log('\nTest C: Both student_auth_token and admin_auth_token (with student token)')
  const testC = await fetch(`${BASE_URL}/api/users`, {
    headers: {
      'Cookie': `student_auth_token=${studentToken}; admin_auth_token=${studentToken}`
    }
  })
  console.log(`Result: ${testC.status}`)
  if (!testC.ok) {
    const errorC = await testC.json()
    console.log('Error:', errorC)
  }

  // Test D: No cookies
  console.log('\nTest D: No cookies')
  const testD = await fetch(`${BASE_URL}/api/users`)
  console.log(`Result: ${testD.status}`)
  if (!testD.ok) {
    const errorD = await testD.json()
    console.log('Error:', errorD)
  }

  // Step 3: Login as admin and test
  console.log('\n3. Login as admin and test...')
  const adminLogin = await fetch(`${BASE_URL}/api/auth/admin/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'testadmin', password: 'password123' })
  })

  const adminSetCookieHeader = adminLogin.headers.get('set-cookie')
  const adminTokenMatch = adminSetCookieHeader?.match(/admin_auth_token=([^;]+)/)
  const adminToken = adminTokenMatch ? adminTokenMatch[1] : null

  if (adminToken) {
    console.log('\nTest E: Valid admin token')
    const testE = await fetch(`${BASE_URL}/api/users`, {
      headers: {
        'Cookie': `admin_auth_token=${adminToken}`
      }
    })
    console.log(`Result: ${testE.status}`)
    if (testE.ok) {
      console.log('✅ Admin token works correctly')
    }
  }

  // Step 4: Decode tokens to see what's inside
  console.log('\n4. Decoding tokens...')

  function decodeJWT(token) {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return null

      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString())
      return payload
    } catch (error) {
      return null
    }
  }

  const studentPayload = decodeJWT(studentToken)
  console.log('Student token payload:', studentPayload)

  if (adminToken) {
    const adminPayload = decodeJWT(adminToken)
    console.log('Admin token payload:', adminPayload)
  }
}

debugCookies().catch(console.error)
