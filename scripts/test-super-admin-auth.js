/**
 * Test super admin authentication
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

async function testSuperAdminAuth() {
  console.log('🧪 Testing Super Admin Authentication\n')

  // Step 1: Login as super admin
  console.log('1. Login as super admin...')
  const loginResponse = await fetch(`${BASE_URL}/api/auth/admin/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'testsuperadmin', password: 'password123' })
  })

  console.log('Login status:', loginResponse.status)

  if (!loginResponse.ok) {
    const errorData = await loginResponse.json()
    console.error('Login failed:', errorData)
    return
  }

  const loginData = await loginResponse.json()
  console.log('Login successful! User role:', loginData.admin?.role)

  // Extract auth token
  const setCookie = loginResponse.headers.get('set-cookie')
  const tokenMatch = setCookie?.match(/admin_auth_token=([^;]+)/)
  const token = tokenMatch ? tokenMatch[1] : null

  if (!token) {
    console.error('No auth token found')
    return
  }

  console.log('Auth token extracted successfully\n')

  // Step 2: Test various endpoints
  const endpoints = [
    { name: 'Users API', url: '/api/users', expectedStatus: 200 },
    { name: 'Admins API', url: '/api/admins', expectedStatus: 200 },
    { name: 'Classes API', url: '/api/classes', expectedStatus: 200 },
    { name: 'Sessions API', url: '/api/admin/sessions', expectedStatus: 200 },
    { name: 'Session Stats API', url: '/api/admin/sessions/stats', expectedStatus: 200 }
  ]

  console.log('2. Testing API endpoints...\n')

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint.name}...`)
      
      const response = await fetch(`${BASE_URL}${endpoint.url}`, {
        headers: {
          'Cookie': `admin_auth_token=${token}`
        }
      })

      console.log(`  Status: ${response.status} (expected: ${endpoint.expectedStatus})`)
      
      if (response.ok) {
        console.log(`  ✅ ${endpoint.name} - SUCCESS`)
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.log(`  ❌ ${endpoint.name} - FAILED: ${errorData.error || 'Unknown error'}`)
      }
      
    } catch (error) {
      console.log(`  ❌ ${endpoint.name} - ERROR: ${error.message}`)
    }
    
    console.log('')
  }

  // Step 3: Test token payload
  console.log('3. Checking token payload...')
  
  function decodeJWT(token) {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return null
      
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString())
      return payload
    } catch (error) {
      return null
    }
  }

  const payload = decodeJWT(token)
  if (payload) {
    console.log('Token payload:')
    console.log('  User ID:', payload.id)
    console.log('  Role:', payload.role)
    console.log('  Session ID:', payload.sessionId || 'none')
    console.log('  Device ID:', payload.deviceId || 'none')
    console.log('  Expires:', new Date(payload.exp * 1000).toISOString())
  } else {
    console.log('Failed to decode token')
  }
}

testSuperAdminAuth().catch(console.error)
