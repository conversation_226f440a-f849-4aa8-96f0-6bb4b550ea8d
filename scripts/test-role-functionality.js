/**
 * Test role-based functionality via API calls
 * This script tests the role-based access control system
 */

const { default: fetch } = require('node-fetch')

const BASE_URL = 'http://localhost:3001'

// Test user credentials
const testUsers = {
  superAdmin: { username: 'testsuperadmin', password: 'password123', role: 'super_admin' },
  admin: { username: 'testadmin', password: 'password123', role: 'admin' },
  student: { username: 'teststudent', password: 'password123', role: 'student' }
}

// Login and get auth token
async function loginUser(username, password, userType = 'admin') {
  const endpoint = userType === 'admin' ? '/api/auth/admin/login' : '/api/auth/student/login'

  const response = await fetch(`${BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  })

  if (!response.ok) {
    throw new Error(`<PERSON><PERSON> failed for ${username}`)
  }

  // Extract auth token from Set-Cookie header
  const setCookieHeader = response.headers.get('set-cookie')
  if (setCookieHeader) {
    const tokenMatch = setCookieHeader.match(/(admin_auth_token|student_auth_token)=([^;]+)/)
    if (tokenMatch) {
      return { token: tokenMatch[2], tokenName: tokenMatch[1] }
    }
  }

  throw new Error('Auth token not found')
}

// Test API endpoint access
async function testApiAccess(endpoint, authToken, tokenName, expectedStatus = 200) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Cookie': `${tokenName}=${authToken}`
      }
    })

    return {
      status: response.status,
      success: response.status === expectedStatus,
      endpoint
    }
  } catch (error) {
    return {
      status: 'ERROR',
      success: false,
      endpoint,
      error: error.message
    }
  }
}

// Test role-based API access
async function testRoleBasedAccess() {
  console.log('🧪 Testing Role-Based API Access...\n')

  const results = {
    superAdmin: [],
    admin: [],
    student: []
  }

  // Test Super Admin access
  try {
    console.log('Testing Super Admin access...')
    const { token, tokenName } = await loginUser(testUsers.superAdmin.username, testUsers.superAdmin.password)

    const superAdminEndpoints = [
      '/api/users',           // Should have access
      '/api/admins',          // Should have access
      '/api/admin/sessions',  // Should have access
      '/api/absence/record'   // Should have access (with proper data)
    ]

    for (const endpoint of superAdminEndpoints) {
      const result = await testApiAccess(endpoint, token, tokenName)
      results.superAdmin.push(result)
      console.log(`  ${result.success ? '✅' : '❌'} ${endpoint} - Status: ${result.status}`)
    }
  } catch (error) {
    console.error(`❌ Super Admin test failed: ${error.message}`)
  }

  console.log('')

  // Test Admin access
  try {
    console.log('Testing Admin access...')
    const { token, tokenName } = await loginUser(testUsers.admin.username, testUsers.admin.password)

    const adminEndpoints = [
      { endpoint: '/api/absence/record', expectedStatus: 400 }, // Should have access but need data
      { endpoint: '/api/users', expectedStatus: 200 },          // Should have access
      { endpoint: '/api/admin/sessions', expectedStatus: 403 }  // Should NOT have access (super admin only)
    ]

    for (const { endpoint, expectedStatus } of adminEndpoints) {
      const result = await testApiAccess(endpoint, token, tokenName, expectedStatus)
      results.admin.push(result)
      console.log(`  ${result.success ? '✅' : '❌'} ${endpoint} - Status: ${result.status} (expected: ${expectedStatus})`)
    }
  } catch (error) {
    console.error(`❌ Admin test failed: ${error.message}`)
  }

  console.log('')

  // Test Student access to admin endpoints (should be denied)
  try {
    console.log('Testing Student access to admin endpoints...')
    const { token, tokenName } = await loginUser(testUsers.student.username, testUsers.student.password, 'student')

    // Test admin endpoints with student token - these should all fail
    const studentToAdminEndpoints = [
      { endpoint: '/api/users', expectedStatus: 401 },          // Admin endpoint with student token
      { endpoint: '/api/admins', expectedStatus: 401 },         // Admin endpoint with student token
      { endpoint: '/api/absence/record', expectedStatus: 401 }  // Admin endpoint with student token
    ]

    for (const { endpoint, expectedStatus } of studentToAdminEndpoints) {
      // Try to access admin endpoint with student token - should fail
      const result = await testApiAccess(endpoint, token, 'admin_auth_token', expectedStatus)
      results.student.push(result)
      console.log(`  ${result.success ? '✅' : '❌'} ${endpoint} with student token - Status: ${result.status} (expected: ${expectedStatus})`)
    }
  } catch (error) {
    console.error(`❌ Student test failed: ${error.message}`)
  }

  return results
}

// Test attendance type validation
async function testAttendanceTypeValidation() {
  console.log('\n🎯 Testing Attendance Type Validation...\n')

  try {
    // Login as admin (limited attendance types)
    const { token, tokenName } = await loginUser(testUsers.admin.username, testUsers.admin.password)

    console.log('Testing admin attendance type restrictions...')

    // Test with valid attendance type for admin
    const validTest = await fetch(`${BASE_URL}/api/absence/record`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `${tokenName}=${token}`
      },
      body: JSON.stringify({
        uniqueCode: '81903e00-b56f-4895-92ed-37b1066400ac', // Test student's unique code
        type: 'Zuhr' // Valid for admin
      })
    })

    console.log(`  ✅ Valid attendance type (Zuhr): ${validTest.status === 200 || validTest.status === 409 ? 'ALLOWED' : 'BLOCKED'}`)

    // Note: We can't easily test invalid attendance types without modifying the enum
    // But the validation is in place in the code

  } catch (error) {
    console.error(`❌ Attendance type test failed: ${error.message}`)
  }
}

// Generate test report
async function generateTestReport() {
  console.log('🎯 ROLE-BASED ACCESS CONTROL TESTING REPORT')
  console.log('='.repeat(50))

  try {
    // Test API access
    const apiResults = await testRoleBasedAccess()

    // Test attendance validation
    await testAttendanceTypeValidation()

    // Summary
    console.log('\n📊 Test Summary:')
    console.log('================')

    const superAdminPassed = apiResults.superAdmin.every(r => r.success)
    const adminPassed = apiResults.admin.every(r => r.success)
    const studentPassed = apiResults.student.every(r => r.success)

    console.log(`Super Admin API Access: ${superAdminPassed ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Admin API Access: ${adminPassed ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Student API Access: ${studentPassed ? '✅ PASS' : '❌ FAIL'}`)

    const allPassed = superAdminPassed && adminPassed && studentPassed

    console.log('\n🎯 Overall Result:')
    console.log(`Role-based access control: ${allPassed ? '✅ WORKING' : '❌ NEEDS ATTENTION'}`)

    console.log('\n📋 Manual Testing Required:')
    console.log('===========================')
    console.log('1. Login as each user type and verify navigation menus')
    console.log('2. Test page access by trying to visit unauthorized URLs')
    console.log('3. Test scanner functionality with different roles')
    console.log('4. Verify attendance type restrictions in scanner UI')

    console.log('\n🌐 Test URLs:')
    console.log('=============')
    console.log('Super Admin: http://localhost:3001/admin (testsuperadmin/password123)')
    console.log('Admin: http://localhost:3001/admin (testadmin/password123)')
    console.log('Student: http://localhost:3001/student (teststudent/password123)')

    return allPassed

  } catch (error) {
    console.error('💥 Test report generation failed:', error)
    return false
  }
}

// Main function
async function main() {
  const success = await generateTestReport()
  process.exit(success ? 0 : 1)
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { main, testRoleBasedAccess, testAttendanceTypeValidation }
