'use client'

import { useRouter } from 'next/navigation'
import { Home, User } from 'lucide-react'
// SECURE: Import the role configuration
import { getNavigationItems } from '@/lib/config/role-permissions'

interface StudentBottomNavProps {
  activeTab: 'home' | 'profile'
}

export function StudentBottomNav({ activeTab }: StudentBottomNavProps) {
  const router = useRouter()

  // SECURE: Get navigation items for student role
  const navItems = getNavigationItems('student')

  // Icon mapping
  const iconMap = {
    home: Home,
    user: User,
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 flex h-16 items-center justify-around border-t border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 md:hidden">
      {navItems.map(item => {
        const IconComponent = iconMap[item.icon as keyof typeof iconMap] || User
        const isActive = activeTab === getActiveTabFromPath(item.path)

        return (
          <button
            key={item.path}
            onClick={() => router.push(item.path)}
            className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
              isActive
                ? 'text-indigo-600 dark:text-indigo-400'
                : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
            }`}
          >
            <IconComponent className="h-5 w-5" />
            <span className="mt-1 text-xs">{item.label}</span>
          </button>
        )
      })}
    </div>
  )
}

// Helper function to determine active tab from path
function getActiveTabFromPath(path: string): string {
  if (path === '/student/home') return 'home'
  if (path === '/student/profile') return 'profile'
  return 'home'
}
